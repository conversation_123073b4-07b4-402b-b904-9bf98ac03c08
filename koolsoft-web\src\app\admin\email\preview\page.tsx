'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Edit, RefreshCw } from 'lucide-react';
import { EmailPreviewForm } from '@/components/admin/email-preview-form';
import { EmailPreviewRenderer } from '@/components/admin/email-preview-renderer';
import { showErrorToast } from '@/lib/toast';

// Type definitions
interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  bodyHtml?: string;
  body?: string;
  variables?: string[];
}

interface PreviewData {
  [key: string]: string;
}

/**
 * Email Preview Page
 *
 * This page allows administrators to preview email templates with test data
 * without actually sending the emails.
 */
export default function EmailPreviewPage() {
  const [loading, setLoading] = useState<boolean>(false);
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
  const [previewData, setPreviewData] = useState<PreviewData>({});
  const [previewHtml, setPreviewHtml] = useState<string>('');
  const [previewSubject, setPreviewSubject] = useState<string>('');
  const router = useRouter();

  // Fetch email templates on component mount
  useEffect(() => {
    fetchTemplates();
  }, []);

  // Fetch all email templates
  const fetchTemplates = async () => {
    try {
      setLoading(true);

      // First try the admin-specific endpoint
      let response = await fetch('/api/admin/email/templates');

      // If that fails, try the regular endpoint
      if (!response.ok) {
        console.warn(`Admin API error (${response.status}), trying regular endpoint`);
        response = await fetch('/api/email/templates');
      }

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error (${response.status}):`, errorText);
        showErrorToast(`Failed to fetch email templates: ${response.status} ${response.statusText}`);
        return;
      }

      const data = await response.json();
      console.log('Templates API response:', data); // Debug log

      if (data.templates && data.templates.length > 0) {
        setTemplates(data.templates);
        // Select the first template by default
        setSelectedTemplate(data.templates[0]);
        // Initialize preview data with empty values for all variables
        initializePreviewData(data.templates[0]);
      } else {
        console.warn('No email templates found in the database');
        showErrorToast('No email templates found. Please create at least one template.');
      }
    } catch (error: unknown) {
      console.error('Error fetching email templates:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      showErrorToast(`Failed to fetch email templates: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Initialize preview data with empty values for all variables
  const initializePreviewData = (template: EmailTemplate | null) => {
    if (!template) return;

    // Use variables from template if available
    if (template.variables && template.variables.length > 0) {
      // Create initial data object with empty values
      const initialData: PreviewData = {};
      template.variables.forEach((variable: string) => {
        initialData[variable] = '';
      });

      setPreviewData(initialData);
      generatePreview(template.id, initialData);
      return;
    }

    // Extract variables from template
    const variableRegex = /{{([^{}]+)}}/g;
    const bodyContent = template.bodyHtml || template.body || '';
    const bodyVariables = [...bodyContent.matchAll(variableRegex)].map(match => match[1]);
    const subjectVariables = [...template.subject.matchAll(variableRegex)].map(match => match[1]);
    const allVariables = [...new Set([...bodyVariables, ...subjectVariables])];

    // Create initial data object with empty values
    const initialData: PreviewData = {};
    allVariables.forEach((variable: string) => {
      initialData[variable] = '';
    });

    setPreviewData(initialData);
    generatePreview(template.id, initialData);
  };

  // Handle template selection change
  const handleTemplateChange = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(template);
      initializePreviewData(template);
    }
  };

  // Handle preview data change
  const handleDataChange = (data: PreviewData) => {
    setPreviewData(data);
    if (selectedTemplate) {
      generatePreview(selectedTemplate.id, data);
    }
  };

  // Generate preview using the API
  const generatePreview = async (templateId: string, data: PreviewData) => {
    try {
      setLoading(true);
      const response = await fetch('/api/email/preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          templateId,
          data,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Preview API error (${response.status}):`, errorText);
        showErrorToast(`Failed to generate preview: ${response.status} ${response.statusText}`);
        return;
      }

      const result = await response.json();
      console.log('Preview API response:', result); // Debug log

      if (result.preview) {
        setPreviewHtml(result.preview.html);
        setPreviewSubject(result.preview.subject);
      } else {
        showErrorToast(result.error || 'Failed to generate preview: No preview data returned');
      }
    } catch (error: unknown) {
      console.error('Error generating preview:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      showErrorToast(`Failed to generate preview: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  // Navigate to template edit page
  const handleEditTemplate = () => {
    if (selectedTemplate) {
      router.push(`/admin/email/templates/edit/${selectedTemplate.id}`);
    }
  };

  return (
    <div className="container mx-auto py-6">
      {loading && templates.length === 0 ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle className="text-2xl font-bold text-black">Email Template Preview</CardTitle>
                <CardDescription>
                  Preview how email templates will look when sent to recipients
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEditTemplate}
                  disabled={!selectedTemplate}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Template
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => selectedTemplate && generatePreview(selectedTemplate.id, previewData)}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh Preview
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="preview" className="w-full">
                <TabsList className="mb-4">
                  <TabsTrigger value="preview">Preview</TabsTrigger>
                  <TabsTrigger value="data">Test Data</TabsTrigger>
                </TabsList>

                <TabsContent value="preview">
                  <EmailPreviewRenderer
                    subject={previewSubject}
                    html={previewHtml}
                    loading={loading}
                  />
                </TabsContent>

                <TabsContent value="data">
                  <EmailPreviewForm
                    templates={templates}
                    selectedTemplateId={selectedTemplate?.id}
                    onTemplateChange={handleTemplateChange}
                    previewData={previewData}
                    onDataChange={handleDataChange}
                  />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
