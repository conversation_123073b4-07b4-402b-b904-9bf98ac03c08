import { prisma } from '@/lib/prisma';
import { hash } from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';

/**
 * Admin User Management Tests
 * 
 * These tests verify the functionality of the admin user management API routes.
 */
describe('Admin User Management', () => {
  // Test users
  const adminUser = {
    id: uuidv4(),
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'Admin123!',
    role: 'ADMIN',
    isActive: true,
  };
  
  const regularUser = {
    id: uuidv4(),
    name: 'Regular User',
    email: '<EMAIL>',
    password: 'User123!',
    role: 'USER',
    isActive: true,
  };
  
  const testUsers = [
    {
      id: uuidv4(),
      name: 'Test User 1',
      email: '<EMAIL>',
      password: 'Test123!',
      role: 'USER',
      isActive: true,
    },
    {
      id: uuidv4(),
      name: 'Test User 2',
      email: '<EMAIL>',
      password: 'Test123!',
      role: 'MANAGER',
      isActive: false,
    },
  ];
  
  // Setup: Create test users before tests
  beforeAll(async () => {
    // Hash passwords
    const hashedAdminPassword = await hash(adminUser.password, 10);
    const hashedRegularPassword = await hash(regularUser.password, 10);
    const hashedTestPassword1 = await hash(testUsers[0].password, 10);
    const hashedTestPassword2 = await hash(testUsers[1].password, 10);
    
    // Create admin user
    await prisma.users.upsert({
      where: { email: adminUser.email },
      update: {
        name: adminUser.name,
        password: hashedAdminPassword,
        role: adminUser.role,
        isActive: adminUser.isActive,
      },
      create: {
        id: adminUser.id,
        name: adminUser.name,
        email: adminUser.email,
        password: hashedAdminPassword,
        role: adminUser.role,
        isActive: adminUser.isActive,
      },
    });
    
    // Create regular user
    await prisma.users.upsert({
      where: { email: regularUser.email },
      update: {
        name: regularUser.name,
        password: hashedRegularPassword,
        role: regularUser.role,
        isActive: regularUser.isActive,
      },
      create: {
        id: regularUser.id,
        name: regularUser.name,
        email: regularUser.email,
        password: hashedRegularPassword,
        role: regularUser.role,
        isActive: regularUser.isActive,
      },
    });
    
    // Create test users
    for (const user of testUsers) {
      await prisma.users.upsert({
        where: { email: user.email },
        update: {
          name: user.name,
          password: user === testUsers[0] ? hashedTestPassword1 : hashedTestPassword2,
          role: user.role,
          isActive: user.isActive,
        },
        create: {
          id: user.id,
          name: user.name,
          email: user.email,
          password: user === testUsers[0] ? hashedTestPassword1 : hashedTestPassword2,
          role: user.role,
          isActive: user.isActive,
        },
      });
    }
  });
  
  // Cleanup: Delete test users after tests
  afterAll(async () => {
    // Delete test users
    for (const user of testUsers) {
      await prisma.users.delete({
        where: { email: user.email },
      }).catch(() => {
        // Ignore errors if user doesn't exist
      });
    }
    
    // Close Prisma connection
    await prisma.$disconnect();
  });
  
  // Test: Get users
  it('should get users with pagination and filtering', async () => {
    // Mock session
    const mockSession = {
      user: {
        id: adminUser.id,
        name: adminUser.name,
        email: adminUser.email,
        role: adminUser.role,
      },
    };
    
    // Mock request
    const mockReq = {
      url: 'http://localhost:3000/api/admin/users?skip=0&take=10',
    };
    
    // Mock response
    const mockRes = {
      json: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
    };
    
    // Mock getServerSession
    jest.mock('next-auth', () => ({
      getServerSession: jest.fn().mockResolvedValue(mockSession),
    }));
    
    // Import the route handler
    const { GET } = require('@/app/api/admin/users/route');
    
    // Call the route handler
    await GET(mockReq, mockRes);
    
    // Expect response to be called with users
    expect(mockRes.json).toHaveBeenCalled();
    expect(mockRes.status).not.toHaveBeenCalledWith(401);
    expect(mockRes.status).not.toHaveBeenCalledWith(403);
  });
  
  // Test: Create user
  it('should create a new user', async () => {
    // Mock session
    const mockSession = {
      user: {
        id: adminUser.id,
        name: adminUser.name,
        email: adminUser.email,
        role: adminUser.role,
      },
    };
    
    // New user data
    const newUser = {
      name: 'New Test User',
      email: '<EMAIL>',
      password: 'NewTest123!',
      role: 'USER',
      isActive: true,
    };
    
    // Mock request
    const mockReq = {
      json: jest.fn().mockResolvedValue(newUser),
    };
    
    // Mock response
    const mockRes = {
      json: jest.fn().mockReturnThis(),
      status: jest.fn().mockReturnThis(),
    };
    
    // Mock getServerSession
    jest.mock('next-auth', () => ({
      getServerSession: jest.fn().mockResolvedValue(mockSession),
    }));
    
    // Import the route handler
    const { POST } = require('@/app/api/admin/users/route');
    
    // Call the route handler
    await POST(mockReq, mockRes);
    
    // Expect response to be called with created user
    expect(mockRes.json).toHaveBeenCalled();
    expect(mockRes.status).not.toHaveBeenCalledWith(401);
    expect(mockRes.status).not.toHaveBeenCalledWith(403);
    
    // Cleanup: Delete the created user
    await prisma.users.delete({
      where: { email: newUser.email },
    }).catch(() => {
      // Ignore errors if user doesn't exist
    });
  });
  
  // Add more tests for other API routes as needed
});
