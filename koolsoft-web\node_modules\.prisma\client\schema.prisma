generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextSearchPostgres"]
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DATABASE_DIRECT_URL")
}

// Legacy User model - kept for backward compatibility with USERS table
model LegacyUser {
  id        Int     @id @default(0) @map("ID")
  name      String? @map("Name") @db.VarChar(50)
  Phone     String? @db.VarChar(15)
  Desig     String? @db.VarChar(2)
  DescDesig String? @db.VarChar(50)

  @@index([id], map: "USERS_ID")
  @@map("USERS")
}

model LegacyCustomer {
  id              Int     @id @default(0) @map("ID")
  name            String? @map("Name") @db.VarChar(50)
  address         String? @map("CustAdd") @db.VarChar(255)
  location        String? @map("Location") @db.VarChar(50)
  phone1          String? @map("CustPhone1") @db.VarChar(15)
  phone2          String? @map("CustPhone2") @db.VarChar(15)
  phone3          String? @map("CustPhone3") @db.VarChar(15)
  mobile          String? @map("MobileNo") @db.VarChar(15)
  fax             String? @map("CustFax") @db.VarChar(15)
  email           String? @map("CustEmail") @db.VarChar(50)
  birthDate       String? @map("BirthDate") @db.VarChar(50)
  anniversaryDate String? @map("AnnDate") @db.VarChar(50)
  birthYear       String? @map("BirthYear") @db.VarChar(50)
  anniversaryYear String? @map("AnnYear") @db.VarChar(50)
  segmentId       Int?    @default(0) @map("SegId")
  designation     String? @map("Desig") @db.VarChar(50)
  visitCardPath   String? @map("VisitCard") @db.VarChar(50)

  @@index([id], map: "CUSTOMERS_ID")
  @@index([segmentId], map: "CUSTOMERS_SegId")
  @@map("CUSTOMERS")
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model Customer {
  id                      String                   @id @default(uuid())
  name                    String
  address                 String?
  city                    String?
  state                   String?
  pinCode                 String?                  @map("pin_code")
  phone                   String?
  fax                     String?
  email                   String?
  mobile                  String?
  website                 String?
  freshness               Int?                     @default(0)
  lastContact             DateTime?                @map("last_contact")
  isActive                Boolean                  @default(true) @map("is_active")
  originalId              Int?                     @map("original_id")
  phone1                  String?                  @map("phone_1")
  phone2                  String?                  @map("phone_2")
  phone3                  String?                  @map("phone_3")
  location                String?
  birthDate               DateTime?                @map("birth_date")
  birthYear               String?                  @map("birth_year")
  anniversaryDate         DateTime?                @map("anniversary_date")
  anniversaryYear         String?                  @map("anniversary_year")
  segmentId               Int?                     @map("segment_id")
  designation             String?
  visitCardPath           String?                  @map("visit_card_path")
  createdAt               DateTime                 @default(now()) @map("created_at")
  updatedAt               DateTime                 @updatedAt @map("updated_at")
  amcContracts            amc_contracts[]
  contacts                Contact[]
  historyCards            history_cards[]
  out_warranties          out_warranties[]
  salesLeads              sales_leads[]
  salesOpportunities      sales_opportunities[]
  salesOrders             sales_orders[]
  salesProspects          sales_prospects[]
  serviceReports          service_reports[]
  visitCards              VisitCard[]
  warranties              warranties[]
  quotations              quotations[]
  salesNotificationEvents SalesNotificationEvent[]

  @@index([name])
  @@index([email])
  @@index([mobile])
  @@index([originalId])
  @@index([isActive], map: "idx_customers_is_active")
  @@index([location], map: "idx_customers_location")
  @@index([phone], map: "idx_customers_phone")
  @@map("customers")
}

model Contact {
  id           String          @id @default(uuid())
  customerId   String          @map("customer_id")
  name         String
  designation  String?
  phone        String?
  email        String?
  isPrimary    Boolean         @default(false) @map("is_primary")
  createdAt    DateTime        @default(now()) @map("created_at")
  updatedAt    DateTime        @updatedAt @map("updated_at")
  amcContracts amc_contracts[]
  customer     Customer        @relation(fields: [customerId], references: [id])
  warranties   warranties[]

  @@index([customerId])
  @@map("contacts")
}

model VisitCard {
  id         String   @id @default(uuid())
  customerId String   @map("customer_id")
  filePath   String   @map("file_path")
  uploadDate DateTime @default(now()) @map("upload_date")
  notes      String?
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")
  customer   Customer @relation(fields: [customerId], references: [id])

  @@index([customerId])
  @@map("visit_cards")
}

model Brand {
  id   Int     @id @default(0) @map("ID")
  name String? @map("Name") @db.VarChar(50)

  @@index([id], map: "BRAND_ID")
  @@map("BRAND")
}

model brands {
  id               String              @id @default(uuid())
  name             String
  description      String?
  isActive         Boolean             @default(true) @map("is_active")
  originalId       Int?                @map("original_id")
  createdAt        DateTime            @default(now()) @map("created_at")
  updatedAt        DateTime            @updatedAt @map("updated_at")
  amcMachines      amc_machines[]
  products         products[]
  warrantyMachines warranty_machines[]
  quotationItems   quotation_items[]

  @@index([name])
  @@index([originalId])
}

model Product {
  id      Int     @id @default(0) @map("ID")
  name    String? @map("Name") @db.VarChar(50)
  TaplTax Float?  @default(0)
  BslTax  Float?  @default(0)

  @@index([id], map: "PRODUCT_ID")
  @@map("PRODUCT")
}

model products {
  id               String              @id @default(uuid())
  name             String
  description      String?
  isActive         Boolean             @default(true) @map("is_active")
  originalId       Int?                @map("original_id")
  taplTax          Float?              @map("tapl_tax")
  bslTax           Float?              @map("bsl_tax")
  brandId          String?             @map("brand_id")
  createdAt        DateTime            @default(now()) @map("created_at")
  updatedAt        DateTime            @updatedAt @map("updated_at")
  amcMachines      amc_machines[]
  models           models[]
  brand            brands?             @relation(fields: [brandId], references: [id])
  warrantyMachines warranty_machines[]
  quotationItems   quotation_items[]

  @@index([name])
  @@index([originalId])
  @@index([brandId])
}

model Model {
  id        Int     @id @default(0) @map("ID")
  name      String? @map("Name") @db.VarChar(50)
  productId Int?    @default(0) @map("Prod_Id")
  specs     String? @map("Desc") @db.VarChar(100)
  Ton_cap   Float?  @default(0)
  InstChg   Float?  @default(0)
  NoOfComp  Int?    @default(0)
  BslMRP    Float?  @default(0)
  BslMCP    Float?  @default(0)
  BslCP     Float?  @default(0)
  TaplMRP   Float?  @default(0)
  TaplMCP   Float?  @default(0)
  TaplCP    Float?  @default(0)
  MRPGM     Float?  @default(0)
  MCPGM     Float?  @default(0)

  @@index([id], map: "MODEL_ID")
  @@index([productId], map: "MODEL_Prod_Id")
  @@map("MODEL")
}

model models {
  id                    String                  @id @default(uuid())
  name                  String
  description           String?
  specs                 String?
  tonnage               Float?                  @map("tonnage")
  installCharge         Float?                  @map("install_charge")
  numberOfComponents    Int?                    @map("number_of_components")
  bslMRP                Float?                  @map("bsl_mrp")
  bslMCP                Float?                  @map("bsl_mcp")
  bslCP                 Float?                  @map("bsl_cp")
  taplMRP               Float?                  @map("tapl_mrp")
  taplMCP               Float?                  @map("tapl_mcp")
  taplCP                Float?                  @map("tapl_cp")
  mrpGM                 Float?                  @map("mrp_gm")
  mcpGM                 Float?                  @map("mcp_gm")
  isActive              Boolean                 @default(true) @map("is_active")
  originalId            Int?                    @map("original_id")
  productId             String?                 @map("product_id")
  createdAt             DateTime                @default(now()) @map("created_at")
  updatedAt             DateTime                @updatedAt @map("updated_at")
  amcMachines           amc_machines[]
  product               products?               @relation(fields: [productId], references: [id])
  out_warranty_machines out_warranty_machines[]
  warrantyMachines      warranty_machines[]
  quotationItems        quotation_items[]

  @@index([name])
  @@index([originalId])
  @@index([productId])
}

model AMCContract {
  originalId           Int       @id @default(0) @map("AmcID")
  customerId           Int?      @default(0) @map("CustID")
  executiveId          Int?      @default(0) @map("ExecID")
  contactPerson        String?   @map("Cperson") @db.VarChar(50)
  contactPhone         String?   @map("Phone") @db.VarChar(15)
  natureOfService      String?   @map("Nat_Serv") @db.VarChar(50)
  startDate            DateTime? @map("StDate") @db.Timestamp(6)
  endDate              DateTime? @map("EndDate") @db.Timestamp(6)
  warningDate          DateTime? @map("Wupdate") @db.Timestamp(6)
  amount               Float?    @default(0) @map("AmcAmt")
  bslDebit             Float?    @default(0) @map("BslDebit")
  previousAmount       Float?    @default(0) @map("PrevAmt")
  amcPeriod            Int?      @default(0) @map("Amc_period")
  yearOfCommencement   Int?      @default(0) @map("YearOfCom")
  numberOfMachines     Int?      @default(0) @map("NoMC")
  numberOfServices     Int?      @default(0) @map("Freq")
  renewalFlag          String?   @map("RenewFlg") @db.VarChar(1)
  newId                Int?      @default(0) @map("NewID")
  outId                Int?      @default(0) @map("OutID")
  blstrFlag            String    @default("D") @map("BlstrFlg") @db.VarChar(1)
  paidAmount           Float?    @default(0) @map("PaidAmt")
  fresh                String?   @map("Fresh") @db.VarChar(3)
  numberOfInstallments Int?      @default(0) @map("NoofInst")
  paymentMode          String?   @map("Paymode") @db.VarChar(4)
  totalTonnage         Float?    @map("tot_ton")
  category             String?   @map("category") @db.VarChar(20)

  @@index([originalId], map: "AMC_sum_AmcID")
  @@index([blstrFlag], map: "AMC_sum_BlstrFlg")
  @@index([customerId], map: "AMC_sum_CustID")
  @@index([contactPerson], map: "AMC_sum_Cust_ID")
  @@index([executiveId], map: "AMC_sum_ExecID")
  @@index([fresh], map: "AMC_sum_Fresh")
  @@index([newId], map: "AMC_sum_NewID")
  @@index([outId], map: "AMC_sum_OutID")
  @@map("AMC_sum")
}

model amc_contracts {
  id                   String              @id @default(uuid())
  customerId           String              @map("customer_id")
  executiveId          String?             @map("executive_id")
  contactPersonId      String?             @map("contact_person_id")
  natureOfService      String?             @map("nature_of_service")
  startDate            DateTime            @map("start_date")
  endDate              DateTime            @map("end_date")
  warningDate          DateTime?           @map("warning_date")
  amount               Float               @default(0) @map("amount")
  bslDebit             Float?              @default(0) @map("bsl_debit")
  previousAmount       Float?              @default(0) @map("previous_amount")
  amcPeriod            Int?                @default(0) @map("amc_period")
  yearOfCommencement   Int?                @map("year_of_commencement")
  numberOfMachines     Int                 @default(0) @map("number_of_machines")
  numberOfServices     Int                 @default(0) @map("number_of_services")
  renewalFlag          Boolean             @default(false) @map("renewal_flag")
  blstrFlag            String?             @map("blstr_flag")
  paidAmount           Float?              @default(0) @map("paid_amount")
  fresh                String?             @map("fresh")
  numberOfInstallments Int?                @default(0) @map("number_of_installments")
  paymentMode          String?             @map("payment_mode")
  totalTonnage         Float?              @map("total_tonnage")
  category             String?             @map("category")
  status               String              @default("ACTIVE") @map("status")
  originalId           Int?                @map("original_id")
  createdAt            DateTime            @default(now()) @map("created_at")
  updatedAt            DateTime            @updatedAt @map("updated_at")
  usersId              String?
  contactPerson        Contact?            @relation(fields: [contactPersonId], references: [id])
  customer             Customer            @relation(fields: [customerId], references: [id])
  users                users?              @relation(fields: [usersId], references: [id])
  divisions            amc_divisions[]
  machines             amc_machines[]
  payments             amc_payments[]
  serviceDates         amc_service_dates[]
  historyAmcDetails    HistoryAmcDetail[]
  historyCards         history_cards[]
  outWarranties        out_warranties?

  @@index([customerId])
  @@index([executiveId])
  @@index([contactPersonId])
  @@index([startDate])
  @@index([endDate])
  @@index([originalId])
  @@index([status])
  @@index([status], map: "idx_amc_contracts_status")
  @@index([warningDate], map: "idx_amc_contracts_warning_date")
}

model AMCMachine {
  amcId         Int     @default(0) @map("AmcID")
  PrdID         Int?    @default(0)
  modelId       Int?    @default(0) @map("MdlID")
  BrID          Int?    @default(0)
  location      String? @map("Loct") @db.VarChar(50)
  LoctFlag      String? @db.VarChar(1)
  serialNumber  String? @map("MCSlNo") @db.VarChar(50)
  assetNo       Int     @default(0) @map("AssetNo")
  historyCardNo Int     @default(0) @map("HCNo")
  Section       String? @db.VarChar(1)

  @@id([amcId, assetNo, historyCardNo])
  @@index([amcId], map: "AMC_Machine_AmcID")
  @@index([assetNo], map: "AMC_Machine_AssetNo")
  @@index([BrID], map: "AMC_Machine_BrID")
  @@index([historyCardNo], map: "AMC_Machine_HCNo")
  @@index([modelId], map: "AMC_Machine_MdlID")
  @@index([PrdID], map: "AMC_Machine_PrdID")
  @@map("AMC_Machine")
}

model amc_machines {
  id                    String           @id @default(uuid())
  amcContractId         String           @map("amc_contract_id")
  productId             String?          @map("product_id")
  modelId               String?          @map("model_id")
  brandId               String?          @map("brand_id")
  location              String?
  locationFlag          String?          @map("location_flag")
  serialNumber          String?          @map("serial_number")
  assetNo               Int?             @map("asset_no")
  historyCardNo         Int?             @map("history_card_no")
  section               String?
  originalAmcId         Int?             @map("original_amc_id")
  originalAssetNo       Int?             @map("original_asset_no")
  originalHistoryCardNo Int?             @map("original_history_card_no")
  createdAt             DateTime         @default(now()) @map("created_at")
  updatedAt             DateTime         @updatedAt @map("updated_at")
  components            amc_components[]
  amcContract           amc_contracts    @relation(fields: [amcContractId], references: [id])
  brand                 brands?          @relation(fields: [brandId], references: [id])
  model                 models?          @relation(fields: [modelId], references: [id])
  product               products?        @relation(fields: [productId], references: [id])

  @@index([amcContractId])
  @@index([productId])
  @@index([modelId])
  @@index([brandId])
  @@index([serialNumber])
  @@index([originalAmcId, originalAssetNo, originalHistoryCardNo])
  @@index([originalAmcId, originalAssetNo, originalHistoryCardNo], map: "amc_machines_original_idx")
  @@index([historyCardNo], map: "idx_amc_machines_history_card_no")
  @@index([serialNumber], map: "idx_amc_machines_serial_number")
}

model AMCComponent {
  amcId        Int       @default(0) @map("AmcID")
  assetNo      Int       @default(0) @map("AssetNo")
  componentNo  Int       @default(0) @map("CompNo")
  serialNumber String?   @map("CompSlNo") @db.VarChar(50)
  WDate        DateTime? @db.Timestamp(6)
  Section      String?   @db.VarChar(1)

  @@id([amcId, assetNo, componentNo])
  @@index([amcId], map: "AMC_Comp_AmcID")
  @@index([componentNo], map: "AMC_Comp_CompNo")
  @@index([assetNo], map: "AMC_Comp_DivId")
  @@map("AMC_Comp")
}

model amc_components {
  id                  String       @id @default(uuid())
  machineId           String       @map("machine_id")
  componentNo         Int?         @map("component_no")
  serialNumber        String?      @map("serial_number")
  warrantyDate        DateTime?    @map("warranty_date")
  section             String?
  originalAmcId       Int?         @map("original_amc_id")
  originalAssetNo     Int?         @map("original_asset_no")
  originalComponentNo Int?         @map("original_component_no")
  createdAt           DateTime     @default(now()) @map("created_at")
  updatedAt           DateTime     @updatedAt @map("updated_at")
  machine             amc_machines @relation(fields: [machineId], references: [id])

  @@index([machineId])
  @@index([serialNumber])
  @@index([originalAmcId, originalAssetNo, originalComponentNo])
  @@index([originalAmcId, originalAssetNo, originalComponentNo], map: "amc_components_original_idx")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model AMCPayment {
  amcId       Int?      @default(0) @map("AmcID")
  receiptNo   Int?      @unique @default(0) @map("ReceiptNo")
  paymentDate DateTime? @map("PayDate") @db.Timestamp(6)
  paymentMode String?   @map("Mode") @db.VarChar(2)
  amount      Float?    @default(0) @map("Amt")
  particulars String?   @map("Particulars") @db.VarChar(50)

  @@index([amcId], map: "AMC_pay_AmcID")
  @@map("AMC_pay")
  @@ignore
}

model amc_payments {
  id                String        @id @default(uuid())
  amcContractId     String        @map("amc_contract_id")
  receiptNo         String?       @map("receipt_no")
  paymentDate       DateTime      @map("payment_date")
  paymentMode       String?       @map("payment_mode")
  amount            Float         @default(0)
  particulars       String?
  originalAmcId     Int?          @map("original_amc_id")
  originalReceiptNo Int?          @map("original_receipt_no")
  createdAt         DateTime      @default(now()) @map("created_at")
  updatedAt         DateTime      @updatedAt @map("updated_at")
  amcContract       amc_contracts @relation(fields: [amcContractId], references: [id])

  @@index([amcContractId])
  @@index([receiptNo])
  @@index([paymentDate])
  @@index([originalAmcId, originalReceiptNo])
  @@index([originalAmcId, originalReceiptNo], map: "amc_payments_original_idx")
}

model AMCServiceDate {
  amcId         Int       @default(0) @map("AmcID")
  serviceDate   DateTime  @map("SerDates") @db.Timestamp(6)
  serviceFlag   String?   @map("SerFlag") @db.VarChar(1)
  completedDate DateTime? @map("DoneDt") @db.Timestamp(6)
  serviceNumber Int?      @default(0) @map("SrNo")
  serviceId     Int?      @default(0) @map("ServID")

  @@id([amcId, serviceDate])
  @@index([amcId], map: "AMC_SerDates_AmcID")
  @@index([serviceDate], map: "AMC_SerDates_DivId")
  @@index([serviceId], map: "AMC_SerDates_ServID")
  @@map("AMC_SerDates")
}

model amc_service_dates {
  id                  String        @id @default(uuid())
  amcContractId       String        @map("amc_contract_id")
  serviceDate         DateTime      @map("service_date")
  serviceFlag         String?       @map("service_flag")
  completedDate       DateTime?     @map("completed_date")
  serviceNumber       Int?          @map("service_number")
  serviceId           Int?          @map("service_id")
  originalAmcId       Int?          @map("original_amc_id")
  originalServiceDate DateTime?     @map("original_service_date")
  createdAt           DateTime      @default(now()) @map("created_at")
  updatedAt           DateTime      @updatedAt @map("updated_at")
  amcContract         amc_contracts @relation(fields: [amcContractId], references: [id])

  @@index([amcContractId])
  @@index([serviceDate])
  @@index([completedDate])
  @@index([originalAmcId, originalServiceDate])
  @@index([originalAmcId, originalServiceDate], map: "amc_service_dates_original_idx")
  @@index([serviceDate], map: "idx_amc_service_dates_service_date")
  @@index([serviceFlag], map: "idx_amc_service_dates_service_flag")
}

model Division {
  id   Int     @id @default(0) @map("ID")
  name String? @map("Name") @db.VarChar(50)

  @@index([id], map: "DIVISION_ID")
  @@map("DIVISION")
}

model divisions {
  id           String          @id @default(uuid())
  name         String
  description  String?
  isActive     Boolean         @default(true) @map("is_active")
  originalId   Int?            @map("original_id")
  createdAt    DateTime        @default(now()) @map("created_at")
  updatedAt    DateTime        @updatedAt @map("updated_at")
  amcDivisions amc_divisions[]

  @@index([name])
  @@index([originalId])
}

model AMCDivision {
  amcId      Int @default(0) @map("AmcID")
  divisionId Int @default(0) @map("DivId")

  @@id([amcId, divisionId])
  @@index([amcId], map: "AMC_div_AmcID")
  @@index([divisionId], map: "AMC_div_DivId")
  @@map("AMC_div")
}

model amc_divisions {
  id                 String        @id @default(uuid())
  amcContractId      String        @map("amc_contract_id")
  divisionId         String        @map("division_id")
  percentage         Float         @default(0)
  isPrimary          Boolean       @default(false) @map("is_primary")
  originalAmcId      Int?          @map("original_amc_id")
  originalDivisionId Int?          @map("original_division_id")
  createdAt          DateTime      @default(now()) @map("created_at")
  updatedAt          DateTime      @updatedAt @map("updated_at")
  amcContract        amc_contracts @relation(fields: [amcContractId], references: [id])
  division           divisions     @relation(fields: [divisionId], references: [id])

  @@unique([amcContractId, divisionId])
  @@index([amcContractId])
  @@index([divisionId])
  @@index([originalAmcId, originalDivisionId])
  @@index([originalAmcId, originalDivisionId], map: "amc_divisions_original_idx")
}

model InWarranty {
  originalId       Int       @id @default(0) @map("InwID")
  customerId       Int?      @default(0) @map("CustID")
  executiveId      Int?      @default(0) @map("ExecID")
  contactPerson    String?   @map("CPerson") @db.VarChar(50)
  contactPhone     String?   @map("Phone") @db.VarChar(15)
  bslNo            String?   @map("BslNo") @db.VarChar(12)
  bslDate          DateTime? @map("BslDate") @db.Timestamp(6)
  bslAmount        Float?    @default(0) @map("BslAmt")
  frequency        Int?      @default(0) @map("Freq")
  numberOfMachines Int?      @default(0) @map("NoMC")
  installDate      DateTime? @map("InstDate") @db.Timestamp(6)
  warrantyDate     DateTime? @map("Wdate") @db.Timestamp(6)
  warningDate      DateTime? @map("Wupdate") @db.Timestamp(6)
  technicianId     Int?      @default(0) @map("TechID")
  amcId            Int?      @default(0) @map("AmcID")
  outId            Int?      @default(0) @map("OutID")

  @@index([customerId], map: "Inwarranty_sum_CustID")
  @@index([executiveId], map: "Inwarranty_sum_ExecID")
  @@index([amcId], map: "Inwarranty_sum_ID")
  @@index([outId], map: "Inwarranty_sum_OutID")
  @@index([technicianId], map: "Inwarranty_sum_TechID")
  @@map("Inwarranty_sum")
}

model warranties {
  id                 String              @id @default(uuid())
  customerId         String              @map("customer_id")
  executiveId        String?             @map("executive_id")
  contactPersonId    String?             @map("contact_person_id")
  bslNo              String?             @map("bsl_no")
  bslDate            DateTime?           @map("bsl_date")
  bslAmount          Float?              @default(0) @map("bsl_amount")
  frequency          Int?                @default(0)
  numberOfMachines   Int                 @default(0) @map("number_of_machines")
  installDate        DateTime?           @map("install_date")
  warrantyDate       DateTime?           @map("warranty_date")
  warningDate        DateTime?           @map("warning_date")
  technicianId       String?             @map("technician_id")
  amcId              String?             @map("amc_id")
  status             String              @default("ACTIVE") @map("status")
  originalId         Int?                @map("original_id")
  createdAt          DateTime            @default(now()) @map("created_at")
  updatedAt          DateTime            @updatedAt @map("updated_at")
  warrantyUpdateDate DateTime?           @map("warranty_update_date")
  usersId            String?
  historyCards       history_cards[]
  contactPerson      Contact?            @relation(fields: [contactPersonId], references: [id])
  customer           Customer            @relation(fields: [customerId], references: [id])
  users              users?              @relation(fields: [usersId], references: [id])
  machines           warranty_machines[]

  @@index([customerId])
  @@index([executiveId])
  @@index([contactPersonId])
  @@index([installDate])
  @@index([warrantyDate])
  @@index([originalId])
  @@index([status])
  @@index([warningDate], map: "idx_warranties_warning_date")
  @@index([warrantyDate], map: "idx_warranties_warranty_date")
}

model InWarrantyMachine {
  inWarrantyId  Int     @default(0) @map("InwID")
  productId     Int?    @default(0) @map("PrdID")
  modelId       Int?    @default(0) @map("MdlID")
  location      String? @map("Loct") @db.VarChar(50)
  locationFlag  String? @map("LoctFlag") @db.VarChar(1)
  serialNumber  String? @map("MCSlNo") @db.VarChar(50)
  assetNo       Int     @default(0) @map("AssetNo")
  historyCardNo Int     @default(0) @map("HCNo")
  section       String? @map("Section") @db.VarChar(1)

  @@id([historyCardNo, assetNo, inWarrantyId])
  @@index([inWarrantyId], map: "Inwarranty_Machine_InwID")
  @@index([modelId], map: "Inwarranty_Machine_MdlID")
  @@index([productId], map: "Inwarranty_Machine_PrdID")
  @@map("Inwarranty_Machine")
}

model warranty_machines {
  id                    String                @id @default(uuid())
  warrantyId            String                @map("warranty_id")
  productId             String?               @map("product_id")
  modelId               String?               @map("model_id")
  brandId               String?               @map("brand_id")
  location              String?
  locationFlag          String?               @map("location_flag")
  serialNumber          String?               @map("serial_number")
  assetNo               Int?                  @map("asset_no")
  historyCardNo         Int?                  @map("history_card_no")
  section               String?
  originalWarrantyId    Int?                  @map("original_warranty_id")
  originalAssetNo       Int?                  @map("original_asset_no")
  originalHistoryCardNo Int?                  @map("original_history_card_no")
  createdAt             DateTime              @default(now()) @map("created_at")
  updatedAt             DateTime              @updatedAt @map("updated_at")
  components            warranty_components[]
  brand                 brands?               @relation(fields: [brandId], references: [id])
  model                 models?               @relation(fields: [modelId], references: [id])
  product               products?             @relation(fields: [productId], references: [id])
  warranty              warranties            @relation(fields: [warrantyId], references: [id])

  @@index([warrantyId])
  @@index([productId])
  @@index([modelId])
  @@index([brandId])
  @@index([serialNumber])
  @@index([originalWarrantyId, originalAssetNo, originalHistoryCardNo])
  @@index([historyCardNo], map: "idx_warranty_machines_history_card_no")
  @@index([serialNumber], map: "idx_warranty_machines_serial_number")
  @@index([originalWarrantyId, originalAssetNo, originalHistoryCardNo], map: "warranty_machines_original_idx")
}

model InWarrantyComponent {
  inWarrantyId Int       @default(0) @map("InwID")
  assetNo      Int       @default(0) @map("AssetNo")
  componentNo  Int       @default(0) @map("CompNo")
  serialNumber String?   @map("CompSlNo") @db.VarChar(50)
  warrantyDate DateTime? @map("WDate") @db.Timestamp(6)
  section      String?   @map("Section") @db.VarChar(1)

  @@id([inWarrantyId, assetNo, componentNo])
  @@index([assetNo], map: "Inwarranty_Comp_AssetNo")
  @@index([componentNo], map: "Inwarranty_Comp_CompNo")
  @@index([inWarrantyId], map: "Inwarranty_Comp_InwID")
  @@map("Inwarranty_Comp")
}

model warranty_components {
  id                  String            @id @default(uuid())
  machineId           String            @map("machine_id")
  componentNo         Int?              @map("component_no")
  serialNumber        String?           @map("serial_number")
  warrantyDate        DateTime?         @map("warranty_date")
  section             String?
  originalWarrantyId  Int?              @map("original_warranty_id")
  originalAssetNo     Int?              @map("original_asset_no")
  originalComponentNo Int?              @map("original_component_no")
  createdAt           DateTime          @default(now()) @map("created_at")
  updatedAt           DateTime          @updatedAt @map("updated_at")
  machine             warranty_machines @relation(fields: [machineId], references: [id])

  @@index([machineId])
  @@index([serialNumber])
  @@index([originalWarrantyId, originalAssetNo, originalComponentNo])
  @@index([originalWarrantyId, originalAssetNo, originalComponentNo], map: "warranty_components_original_idx")
}

model ACC_DIVISION {
  AccDivId Int     @id @default(0)
  Name     String? @db.VarChar(50)

  @@index([AccDivId], map: "ACC_DIVISION_AccDivId")
}

model AMC_AddOn {
  AmcID    Int     @default(0)
  PrdID    Int?    @default(0)
  MdlID    Int?    @default(0)
  BrID     Int?    @default(0)
  Loct     String? @db.VarChar(50)
  LoctFlag String? @db.VarChar(1)
  MCSlNo   String? @db.VarChar(50)
  AssetNo  Int     @default(0)
  HCNo     Int     @default(0)
  Amt      Float?  @default(0)
  Section  String? @db.VarChar(1)

  @@id([AmcID, AssetNo, HCNo])
  @@index([AmcID], map: "AMC_AddOn_AmcID")
  @@index([AssetNo], map: "AMC_AddOn_AssetNo")
  @@index([BrID], map: "AMC_AddOn_BrID")
  @@index([HCNo], map: "AMC_AddOn_HCNo")
  @@index([MdlID], map: "AMC_AddOn_MdlID")
  @@index([PrdID], map: "AMC_AddOn_PrdID")
}

model AMC_AddOnComp {
  AmcID    Int       @default(0)
  AssetNo  Int       @default(0)
  CompNo   Int       @default(0)
  CompSlNo String?   @db.VarChar(50)
  WDate    DateTime? @db.Timestamp(6)
  Section  String?   @db.VarChar(1)

  @@id([AmcID, AssetNo, CompNo])
  @@index([AmcID], map: "AMC_AddOnComp_AmcID")
  @@index([CompNo], map: "AMC_AddOnComp_CompNo")
  @@index([AssetNo], map: "AMC_AddOnComp_DivId")
}

model AMC_NewLoct {
  AmcID   Int     @default(0)
  AssetNo Int     @default(0)
  NewLoct String? @db.VarChar(50)

  @@id([AmcID, AssetNo])
  @@index([AmcID], map: "AMC_NewLoct_AmcID")
  @@index([AssetNo], map: "AMC_NewLoct_AssetNo")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Amc_Inst {
  AmcID     Int?      @default(0)
  InstDt    DateTime? @db.Timestamp(6)
  Amt       Float?    @default(0)
  ReceiptNo Int?      @default(0)

  @@index([AmcID], map: "Amc_Inst_AmcID")
  @@index([ReceiptNo], map: "Amc_Inst_ReceiptNo")
  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model BluestarAccounts_CNN {
  BlrAccId  Int?      @default(0)
  BlrRecId  Int?      @default(0)
  TotCred   Float?    @default(0)
  CnNo      String?   @db.VarChar(50)
  Month     String?   @db.VarChar(50)
  CnDate    DateTime? @db.Timestamp(6)
  Receipt   Float?    @default(0)
  Deduction Float?    @default(0)
  DedHeadId Int?      @default(0)
  RcptDate  DateTime? @db.Timestamp(6)
  ChequeNo  String?   @db.VarChar(50)
  Balance   Float?    @default(0)
  OSDays    Int?      @default(0)
  WrkOrder  String?   @db.VarChar(50)

  @@index([BlrAccId], map: "BluestarAccounts_CNN_BlrAccId")
  @@index([BlrRecId], map: "BluestarAccounts_CNN_BlrRecId")
  @@index([WrkOrder], map: "BluestarAccounts_CNN_WrkOrder")
  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model BluestarAccounts_Credits {
  BlrAccId  Int?    @default(0)
  BlrRecId  Int?    @default(0)
  Code      String? @db.VarChar(50)
  SalesComp Float?  @default(0)
  SrvComp   Float?  @default(0)
  Install   Float?  @default(0)
  WrkOrder  String? @db.VarChar(50)
  InvAmt    Float?  @default(0)
  Suspense  Float?  @default(0)
  OthCred   Float?  @default(0)

  @@index([BlrAccId], map: "BluestarAccounts_Credits_BlrAccId")
  @@index([BlrRecId], map: "BluestarAccounts_Credits_BlrRecId")
  @@index([Code], map: "BluestarAccounts_Credits_Code")
  @@index([WrkOrder], map: "BluestarAccounts_Credits_WrkOrder")
  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model BluestarAccounts_Product {
  BlrAccId Int?      @default(0)
  ProdId   Int?      @default(0)
  MdlID    Int?      @default(0)
  Qnty     Int?      @default(0)
  InvNo    String?   @db.VarChar(50)
  Amt      Float?    @default(0)
  AmtDate  DateTime? @db.Timestamp(6)
  RcvAmt   Float?    @default(0)
  RcvDate  DateTime? @db.Timestamp(6)
  OSAmt    Float?    @default(0)
  OSDays   Int?      @default(0)

  @@index([BlrAccId], map: "BluestarAccounts_Product_BlrAccId")
  @@index([MdlID], map: "BluestarAccounts_Product_MdlID")
  @@index([ProdId], map: "BluestarAccounts_Product_ProdId")
  @@ignore
}

model BluestarAccounts_Sum {
  BlrAccId Int       @id @default(0)
  CustId   Int?      @default(0)
  BslDate  DateTime? @db.Timestamp(6)
  Mode     String?   @db.VarChar(50)
  AccDivId Int?      @default(0)
  BslExec  String?   @db.VarChar(50)

  @@index([AccDivId], map: "BluestarAccounts_Sum_AccDivId")
  @@index([BlrAccId], map: "BluestarAccounts_Sum_BlrAccId")
  @@index([CustId], map: "BluestarAccounts_Sum_CustId")
}

model CATEGORY_SRV {
  CatSrvId Int     @id @default(0)
  Name     String? @db.VarChar(50)

  @@index([CatSrvId], map: "CATEGORY_SRV_SrvCatId")
}

model COMPETITOR {
  ID   Int     @id @default(0)
  Name String? @db.VarChar(50)

  @@index([ID], map: "COMPETITOR_ID")
}

model COMPLAINT {
  ID   Int     @id @default(0)
  Name String? @db.VarChar(50)

  @@index([ID], map: "COMPLAINT_ID")
}

model CRM {
  RecID       Int       @id @default(0)
  RecDate     DateTime? @db.Timestamp(6)
  CustID      Int?      @default(0)
  CustST      String?   @db.VarChar(2)
  CPer        String?   @db.VarChar(50)
  SegId       Int?      @default(0)
  UserGrpId   Int?      @default(0)
  BrID        Int?      @default(0)
  IsBarred    Boolean?
  SrvRestrict String?   @db.VarChar(50)
  StLevel     String?   @db.VarChar(50)
  Place       String?   @db.VarChar(50)
  Relation    String?   @db.VarChar(50)
  PrId        Int?      @default(0)
  NatSrvId    Int?      @default(0)
  USP         String?   @db.VarChar(50)
  ExecID      Int?      @default(0)
  IsBLost     Boolean?
  BRelation   String?   @db.VarChar(50)

  @@index([BrID], map: "CRM_BrID")
  @@index([CustID], map: "CRM_CustID")
  @@index([CustST], map: "CRM_CustST")
  @@index([ExecID], map: "CRM_ExecID")
  @@index([NatSrvId], map: "CRM_NatSrvId")
  @@index([PrId], map: "CRM_PrId")
  @@index([RecID], map: "CRM_RelID")
  @@index([SegId], map: "CRM_SegId")
  @@index([UserGrpId], map: "CRM_UserGrpId")
}

model Cust_Fresh {
  ID         Int     @id @default(0)
  Name       String? @db.VarChar(50)
  CustAdd    String? @db.VarChar(255)
  CustPhone1 String? @db.VarChar(15)
  CustPhone2 String? @db.VarChar(15)
  CustFax    String? @db.VarChar(15)
  CustEmail  String? @db.VarChar(50)

  @@index([ID], map: "Cust_Fresh_ID")
}

model DEBIT_DIV {
  ID       Int     @id @default(0)
  DebitDiv String? @db.VarChar(50)

  @@index([ID], map: "DEBIT_DIV_ID")
}

model DEDUCTION_HEAD {
  DedHeadId Int     @id @default(0)
  Head      String? @db.VarChar(50)

  @@index([DedHeadId], map: "DEDUCTION_HEAD_DedHeadId")
}

model ENQUIRY {
  ID   Int     @id @default(0)
  Name String? @db.VarChar(50)

  @@index([ID], map: "ENQUIRY_ID")
}

model EnqComp {
  CallID   Int       @id @default(0)
  CDate    DateTime? @db.Timestamp(6)
  CTime    String?   @db.VarChar(20)
  CustId   Int?      @default(0)
  CustST   String?   @db.VarChar(2)
  Status   String?   @db.VarChar(5)
  Remarks  String?   @db.VarChar(50)
  No       Int?      @default(0)
  CallType String?   @db.VarChar(4)
  DTime    Float?
  PFlag    String?   @default("0") @db.VarChar(1)

  @@index([CallType], map: "EnqComp_CallType")
  @@index([CustId], map: "EnqComp_CustId")
  @@index([No], map: "EnqComp_SrNo")
  @@index([Status], map: "EnqComp_Status")
}

model ExecTarget {
  ExecId Int  @default(0)
  Month  Int  @default(0)
  Year   Int  @default(0)
  PrdId  Int  @default(0)
  Target Int? @default(0)
  Billed Int? @default(0)

  @@id([ExecId, Month, Year, PrdId])
  @@index([ExecId], map: "ExecTarget_ExecId")
  @@index([PrdId], map: "ExecTarget_PrdId")
}

model FAILURE {
  ID   Int     @id @default(0)
  Name String? @db.VarChar(50)

  @@index([ID], map: "FAILURE_ID")
}

model Funnel_div {
  FID   Int @default(0)
  DivID Int @default(0)

  @@id([FID, DivID])
  @@index([DivID], map: "Funnel_div_DivID")
  @@index([FID], map: "Funnel_div_FID")
}

model Funnel_sum {
  TID        Int       @default(0)
  TStatus    String    @default("0") @db.VarChar(1)
  FID        Int       @default(0)
  FDate      DateTime? @db.Timestamp(6)
  LogDate    DateTime? @db.Timestamp(6)
  Prospect   String?   @db.VarChar(5)
  ProsPerct  Int?      @default(0)
  FPDate     DateTime? @db.Timestamp(6)
  NVDate     DateTime? @db.Timestamp(6)
  OrdST      String?   @db.VarChar(1)
  LostReason String?   @db.VarChar(100)
  CPerson    String?   @db.VarChar(50)
  Phone      String?   @db.VarChar(15)
  Remarks    String?   @db.VarChar(100)

  @@id([TID, TStatus, FID])
  @@index([FID], map: "Funnel_sum_FID")
  @@index([TID], map: "Funnel_sum_TID")
  @@index([TStatus], map: "Funnel_sum_TStatus")
}

model Gateway_div {
  GID   Int @default(0)
  DivID Int @default(0)

  @@id([GID, DivID])
  @@index([DivID], map: "Gateway_div_DivID")
  @@index([GID], map: "Gateway_div_GID")
}

model Gateway_sum {
  TID        Int       @default(0)
  TStatus    String    @default("0") @db.VarChar(1)
  GID        Int       @default(0)
  GTDate     DateTime? @db.Timestamp(6)
  LogDate    DateTime? @db.Timestamp(6)
  Prospect   String    @db.VarChar(5)
  ProsPerct  Int?      @default(0)
  FPDate     DateTime? @db.Timestamp(6)
  NVDate     DateTime? @db.Timestamp(6)
  OrdST      String    @db.VarChar(1)
  LostReason String?   @db.VarChar(100)
  CPerson    String?   @db.VarChar(50)
  Phone      String?   @db.VarChar(15)
  Remarks    String?   @db.VarChar(100)

  @@id([TID, TStatus, GID])
  @@index([FPDate], map: "Gateway_sum_FPDate")
  @@index([GID], map: "Gateway_sum_GID")
  @@index([GTDate], map: "Gateway_sum_GTDate")
  @@index([OrdST], map: "Gateway_sum_OrdST")
  @@index([TID], map: "Gateway_sum_TID")
  @@index([TStatus], map: "Gateway_sum_TStatus")
}

model HistorySection {
  Card_No Int    @default(0)
  Section String @db.VarChar(1)

  @@id([Card_No, Section])
}

model History_AddOnDet {
  Card_No    Int       @id @default(0)
  WarnNo     String?   @db.VarChar(20)
  HandDate   DateTime? @db.Timestamp(6)
  ExpDate    DateTime? @db.Timestamp(6)
  ExpCmpDate DateTime? @db.Timestamp(6)
}

model History_AmcDet {
  Card_No    Int     @default(0)
  AmcID      Int     @default(0)
  BlkYear    String? @db.VarChar(9)
  Debit      Float?  @default(0)
  NCBonus    Float?  @default(0)
  Increments Float?  @default(0)

  @@id([Card_No, AmcID])
  @@index([AmcID], map: "History_AmcDet_AmcID")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model History_Audit {
  Card_No Int? @default(0)
  Blkid   Int? @default(0)
  Rate    Int? @default(0)

  @@index([Card_No], map: "History_Audit_Card_No")
  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model History_Complaint {
  Card_No     Int?      @default(0)
  AssetNo     Int?      @default(0)
  Calldate    DateTime? @db.Timestamp(6)
  Visitdate   DateTime? @db.Timestamp(6)
  Nat_Comp    String?   @db.VarChar(20)
  CompID      Int?      @default(0)
  Action      String?   @db.VarChar(20)
  ServID      Int?      @default(0)
  SRNo        Int?      @default(0)
  ResTime     String?   @db.VarChar(10)
  ClosePeriod String?   @db.VarChar(10)
  CustAlert   String?   @db.VarChar(20)
  Section     String?   @db.VarChar(1)

  @@index([AssetNo], map: "History_Complaint_AssetNo1")
  @@index([Calldate], map: "History_Complaint_Calldate")
  @@index([Card_No], map: "History_Complaint_Card_No")
  @@index([CompID], map: "History_Complaint_CompID")
  @@index([SRNo], map: "History_Complaint_SRNo")
  @@index([ServID], map: "History_Complaint_ServID")
  @@index([Visitdate], map: "History_Complaint_Visitdate")
  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model History_Maintain {
  Card_No Int?      @default(0)
  Due     DateTime? @db.Timestamp(6)
  Done    DateTime? @db.Timestamp(6)
  Section String?   @db.VarChar(1)

  @@index([Card_No], map: "History_Maintain_Card_No")
  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model History_Repair {
  Card_No Int?    @default(0)
  AssetNo Int?    @default(0)
  Details String? @db.VarChar(50)
  Amt     Float?  @default(0)
  Section String? @db.VarChar(1)

  @@index([AssetNo], map: "History_Repair_AssetNo")
  @@index([Card_No], map: "History_Repair_Card_No")
  @@ignore
}

model History_Sum {
  Card_No   Int     @id @default(0)
  CustID    Int?    @default(0)
  CPerson   String? @db.VarChar(50)
  Phone     String? @db.VarChar(15)
  AddonFlag String? @db.VarChar(1)
  Source    String? @db.VarChar(3)
  ToCardNo  Int?    @default(0)

  @@index([CustID], map: "History_Sum_CustID")
  @@index([ToCardNo], map: "History_Sum_ToCardNo")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model History_Wtr {
  Card_No Int?      @default(0)
  Done    DateTime? @db.Timestamp(6)
  Section String?   @db.VarChar(1)

  @@index([Card_No], map: "History_Wtr_Card_No")
  @@ignore
}

model InwInst_Param {
  InwID   Int     @default(0)
  AssetNo Int     @default(0)
  SP      String? @db.VarChar(50)
  DP      String? @db.VarChar(50)
  AMP     String? @db.VarChar(50)
  VOLT    String? @db.VarChar(50)
  CFM     String? @db.VarChar(50)
  Noise   String? @db.VarChar(50)
  RTemp   String? @db.VarChar(50)
  ATemp   String? @db.VarChar(50)

  @@id([InwID, AssetNo])
  @@index([AssetNo], map: "InwInst_Param_AssetNo")
  @@index([InwID], map: "InwInst_Param_InwID")
}

model Inwarranty_NewLoct {
  InwId   Int     @default(0)
  AssetNo Int     @default(0)
  NewLoct String? @db.VarChar(50)

  @@id([InwId, AssetNo])
  @@index([AssetNo], map: "Inwarranty_NewLoct_AssetNo")
  @@index([InwId], map: "Inwarranty_NewLoct_InwId")
}

model Inwarranty_SerDates {
  InwID    Int       @default(0)
  SerDates DateTime  @db.Timestamp(6)
  SerFlag  String?   @db.VarChar(1)
  DoneDt   DateTime? @db.Timestamp(6)
  SrNo     Int?      @default(0)
  ServID   Int?      @default(0)

  @@id([InwID, SerDates])
  @@index([InwID], map: "Inwarranty_SerDates_InwID")
  @@index([SerDates], map: "Inwarranty_SerDates_SerDates")
  @@index([ServID], map: "Inwarranty_SerDates_ServID")
}

model Inwarranty_TrDam {
  InwID   Int @default(0)
  AssetNo Int @default(0)
  TrID    Int @default(0)

  @@id([InwID, AssetNo, TrID])
  @@index([TrID], map: "Inwarranty_TrDam_DivID")
  @@index([InwID], map: "Inwarranty_TrDam_InwID")
}

model Inwarranty_div {
  InwID Int @default(0)
  DivID Int @default(0)

  @@id([InwID, DivID])
  @@index([DivID], map: "Inwarranty_div_DivID")
  @@index([InwID], map: "Inwarranty_div_InwID")
}

model MovementRegister_Products {
  RegId     Int     @default(0)
  RecId     Int     @default(0)
  ProdId    Int?    @default(0)
  Qnty      Int?    @default(0)
  MacSlno   String? @db.VarChar(50)
  CompSlNo  String? @db.VarChar(50)
  PartName  String? @db.VarChar(50)
  PartSlNo  String? @db.VarChar(50)
  ReceiptNo String? @db.VarChar(50)

  @@id([RegId, RecId])
  @@index([ProdId], map: "MovementRegister_Products_ProdId")
  @@index([RecId], map: "MovementRegister_Products_RecId")
  @@index([RegId], map: "MovementRegister_Products_RegId")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model MovementRegister_Repair {
  RegId     Int?      @default(0)
  RecId     Int?      @default(0)
  Qnty      Int?      @default(0)
  MovedOut  String?   @db.VarChar(50)
  DelNo     String?   @db.VarChar(50)
  DelDate   DateTime? @db.Timestamp(6)
  ReceiptNo String?   @db.VarChar(50)
  RcptDate  DateTime? @db.Timestamp(6)

  @@index([RecId, RegId], map: "MovementRegister_Repair_RecId")
  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model MovementRegister_Repl {
  RegId            Int?      @default(0)
  RecId            Int?      @default(0)
  ReplFrmStore     Boolean?
  RefitAfterRepair Boolean?
  RefitDate        DateTime? @db.Timestamp(6)
  TAT              String?   @db.VarChar(50)

  @@index([RecId, RegId], map: "MovementRegister_Repl_RecId")
  @@ignore
}

model MovementRegister_Sum {
  RegId    Int       @id @default(0)
  CustId   Int?      @default(0)
  DOR      DateTime? @db.Timestamp(6)
  TechId   Int?      @default(0)
  NatSrvId Int?      @default(0)
  PDD      DateTime? @db.Timestamp(6)

  @@index([CustId], map: "MovementRegister_Sum_CustId")
  @@index([NatSrvId], map: "MovementRegister_Sum_NatSrvId")
  @@index([RegId], map: "MovementRegister_Sum_RegId")
  @@index([TechId], map: "MovementRegister_Sum_TechId")
}

model NATURE_SERVICE {
  NatSrvId Int     @id @default(0)
  Name     String? @db.VarChar(50)

  @@index([NatSrvId], map: "NATURE_SERVICE_NatSrvId")
}

model NAT_COMPLAINT {
  ID   Int     @id @default(0)
  Name String? @db.VarChar(50)

  @@index([ID], map: "NAT_COMPLAINT_ID")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model ORG {
  Name     String? @db.VarChar(50)
  Address1 String? @db.VarChar(50)
  Address2 String? @db.VarChar(50)
  Phone    String? @db.VarChar(50)
  email    String? @db.VarChar(50)
  kgst     String? @db.VarChar(50)
  cst      String? @db.VarChar(50)
  Company  String? @db.VarChar(50)

  @@ignore
}

model ORG_DIV {
  ID     Int     @id @default(0)
  OrgDiv String? @db.VarChar(50)

  @@index([ID], map: "ORG_DIV_ID")
}

model OrderForm {
  OrdFrmNo   Int      @id @default(0)
  TID        Int?     @default(0)
  OrdfrmDate DateTime @db.Timestamp(6)
  ToAdd      String?  @db.VarChar(50)
  Attn       String?  @db.VarChar(50)
  OrdPlace   String?  @db.VarChar(50)
  CustOrdNo  String?  @db.VarChar(50)
  CustAdd    String?  @db.VarChar(150)
  Price      Float?   @default(0)
  Payment    String?  @db.VarChar(50)
  DelPlace   String?  @db.VarChar(50)
  WorstDel   String?  @db.VarChar(50)
  ForwById   Int?     @default(0)

  @@index([OrdfrmDate], map: "OrderForm_OrdfrmDate")
  @@index([TID], map: "OrderForm_TID")
}

model OrderForm_Mdl {
  OrdFrmNo Int  @default(0)
  PrdId    Int? @default(0)
  MdlId    Int  @default(0)
  Qty      Int? @default(0)

  @@id([OrdFrmNo, MdlId])
  @@index([MdlId], map: "OrderForm_Mdl_MdlId")
  @@index([PrdId], map: "OrderForm_Mdl_prdId")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model OrderForm_RptTmp {
  OrdFrmNo Int?    @default(0)
  Heading  String? @db.VarChar(50)
  Name     String? @db.VarChar(50)
  Qty      Int?    @default(0)

  @@index([OrdFrmNo], map: "OrderForm_RptTmp_OrdFrmNo")
  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Order_add {
  OrdID Int?    @default(0)
  Add   String? @db.VarChar(255)

  @@index([OrdID], map: "Order_add_OrdID")
  @@ignore
}

model Order_div {
  OrdID Int @default(0)
  DivID Int @default(0)

  @@id([OrdID, DivID])
  @@index([DivID], map: "Order_div_DivID")
  @@index([OrdID], map: "Order_div_OrdID")
}

model Order_pay {
  OrdID       Int?     @default(0)
  ReceiptNo   Int      @unique @default(0)
  PayDate     DateTime @db.Timestamp(6)
  Mode        String   @db.VarChar(2)
  Amt         Float?   @default(0)
  Particulars String?  @db.VarChar(50)
  BlstrFlg    String   @db.VarChar(50)

  @@index([OrdID], map: "Order_pay_OrdID")
}

model Order_sum {
  TID         Int       @default(0)
  TStatus     String    @default("0") @db.VarChar(1)
  OrdID       Int       @default(0)
  OrdDate     DateTime? @db.Timestamp(6)
  LogDate     DateTime? @db.Timestamp(6)
  TaplNo      String?   @db.VarChar(12)
  TaplDate    DateTime? @db.Timestamp(6)
  BslInvNo    String?   @db.VarChar(12)
  BslInvDate  DateTime? @db.Timestamp(6)
  FrmWhere    String?   @db.VarChar(1)
  Prospect    String?   @db.VarChar(5)
  ProsPerct   Int?      @default(0)
  FPDate      DateTime? @db.Timestamp(6)
  DelDate     DateTime? @db.Timestamp(6)
  Delay       Int?      @default(0)
  ActDate     DateTime? @db.Timestamp(6)
  TaplAmt     Float?    @default(0)
  BslAmt      Float?    @default(0)
  BslAdv      Float?    @default(0)
  BslCollect  Float?    @default(0)
  TaplAdv     Float?    @default(0)
  TaplCollect Float?    @default(0)
  DelAdd      String?   @db.VarChar(5)
  InstaChgsTo String?   @db.VarChar(5)
  CPerson     String?   @db.VarChar(50)
  Phone       String?   @db.VarChar(15)
  Remarks     String?   @db.VarChar(100)

  @@id([TID, TStatus, OrdID])
  @@index([OrdID], map: "Order_sum_OrdID")
  @@index([TID], map: "Order_sum_TID")
  @@index([TStatus], map: "Order_sum_TStatus")
}

model OutWnty_Comp {
  OutID    Int       @default(0)
  AssetNo  Int       @default(0)
  CompNo   Int       @default(0)
  CompSlNo String?   @db.VarChar(50)
  WDate    DateTime? @db.Timestamp(6)
  Section  String?   @db.VarChar(1)

  @@id([OutID, AssetNo, CompNo])
  @@index([OutID], map: "OutWnty_Comp_AmcID")
  @@index([CompNo], map: "OutWnty_Comp_CompNo")
  @@index([AssetNo], map: "OutWnty_Comp_DivId")
}

model OutWnty_Machine {
  OutID   Int     @default(0)
  PrdID   Int?    @default(0)
  MdlID   Int?    @default(0)
  BrID    Int?    @default(0)
  Loct    String? @db.VarChar(50)
  MCSlNo  String? @db.VarChar(50)
  AssetNo Int     @default(0)
  HCNo    Int     @default(0)
  Section String? @db.VarChar(1)

  @@id([OutID, AssetNo, HCNo])
  @@index([OutID], map: "OutWnty_Machine_AmcID")
  @@index([AssetNo], map: "OutWnty_Machine_AssetNo")
  @@index([BrID], map: "OutWnty_Machine_BrID")
  @@index([HCNo], map: "OutWnty_Machine_HCNo")
  @@index([MdlID], map: "OutWnty_Machine_MdlID")
  @@index([PrdID], map: "OutWnty_Machine_PrdID")
}

model OutWnty_div {
  OutID Int @default(0)
  DivId Int @default(0)

  @@id([OutID, DivId])
  @@index([OutID], map: "OutWnty_div_AmcID")
  @@index([DivId], map: "OutWnty_div_DivId")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model OutWnty_pay {
  OutID       Int?      @default(0)
  ReceiptNo   Int?      @unique @default(0)
  PayDate     DateTime? @db.Timestamp(6)
  Mode        String?   @db.VarChar(2)
  Amt         Float?    @default(0)
  Particulars String?   @db.VarChar(50)

  @@index([OutID], map: "OutWnty_pay_AmcID")
  @@ignore
}

model OutWnty_sum {
  OutID   Int       @id @default(0)
  OutDate DateTime? @db.Timestamp(6)
  CustID  Int?      @default(0)
  ExecID  Int?      @default(0)
  Cperson String?   @db.VarChar(50)
  Phone   String?   @db.VarChar(15)
  NoMC    Int?      @default(0)
  Source  String?   @db.VarChar(3)
  AmcID   Int?      @default(0)
  InwID   Int?      @default(0)
  ToAmcID Int?      @default(0)

  @@index([OutID], map: "OutWnty_sum_AmcID")
  @@index([CustID], map: "OutWnty_sum_CustID")
  @@index([Cperson], map: "OutWnty_sum_Cust_ID")
  @@index([ExecID], map: "OutWnty_sum_ExecID")
  @@index([AmcID], map: "OutWnty_sum_ID")
  @@index([InwID], map: "OutWnty_sum_InwID")
  @@index([ToAmcID], map: "OutWnty_sum_ToAmcID")
}

model PRIORITY_LOI {
  PrId Int     @id @default(0)
  Name String? @db.VarChar(50)

  @@index([PrId], map: "PRIORITY_LOI_PrId")
}

model PROFORMA_MODEL {
  PMdlId      Int     @id @default(0)
  Name        String? @db.VarChar(50)
  Prod_Id     Int?    @default(0)
  Description String? @db.VarChar(100)
  Price       Float?  @default(0)

  @@index([PMdlId], map: "PROFORMA_MODEL_ID")
  @@index([Prod_Id], map: "PROFORMA_MODEL_Prod_Id")
}

model Perf_Inv_Sum {
  QtNo      Int       @id @default(0)
  QtDate    DateTime? @db.Timestamp(6)
  TID       Int?      @default(0)
  TStatus   String?   @db.VarChar(1)
  RefNo     String?   @db.VarChar(50)
  RefText   String?   @db.VarChar(50)
  Delay     Int?      @default(0)
  InstChg   Float?    @default(0)
  Discount  Float?    @default(0)
  Adv       Float?    @default(0)
  DelAmt    Float?    @default(0)
  CommAmt   Float?    @default(0)
  OrderBsl  String?   @db.VarChar(150)
  OrderTapl String?   @db.VarChar(150)

  @@index([QtNo], map: "Perf_Inv_Sum_PID")
  @@index([QtDate], map: "Perf_Inv_Sum_QtDate")
  @@index([TID], map: "Perf_Inv_Sum_TID")
  @@index([TStatus], map: "Perf_Inv_Sum_TStatus")
}

model Perf_Machine {
  QtNo    Int    @default(0)
  PrdID   Int    @default(0)
  MdlID   Int    @default(0)
  Qty     Int?   @default(0)
  QtPrice Float? @default(0)
  TaxRate Float? @default(0)

  @@id([QtNo, PrdID, MdlID])
  @@index([MdlID], map: "Perf_Machine_MdlID")
  @@index([PrdID], map: "Perf_Machine_PrdID")
  @@index([QtNo], map: "Perf_Machine_TID")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Perf_Others {
  QtNo      Int?    @default(0)
  Part      String? @db.VarChar(50)
  TaxRate   Float?  @default(0)
  Qty       Int?    @default(0)
  Rate      Float?  @default(0)
  TaxAmt    Float?  @default(0)
  NonTaxAmt Float?  @default(0)

  @@index([QtNo], map: "Perf_Others_TID")
  @@ignore
}

model Pipeline_div {
  PPID  Int @default(0)
  DivID Int @default(0)

  @@id([PPID, DivID])
  @@index([DivID], map: "Pipeline_div_DivID")
  @@index([PPID], map: "Pipeline_div_PPID")
}

model Pipeline_sum {
  TID        Int       @default(0)
  TStatus    String    @default("0") @db.VarChar(1)
  PPID       Int       @default(0)
  PDate      DateTime? @db.Timestamp(6)
  LogDate    DateTime? @db.Timestamp(6)
  Prospect   String?   @db.VarChar(5)
  ProsPerct  Int?      @default(0)
  FPDate     DateTime? @db.Timestamp(6)
  NVDate     DateTime? @db.Timestamp(6)
  OrdST      String?   @db.VarChar(1)
  LostReason String?   @db.VarChar(100)
  CPerson    String?   @db.VarChar(50)
  Phone      String?   @db.VarChar(15)
  Remarks    String?   @db.VarChar(100)

  @@id([TID, TStatus, PPID])
  @@index([PPID], map: "Pipeline_sum_PPID")
  @@index([TID], map: "Pipeline_sum_TID")
  @@index([TStatus], map: "Pipeline_sum_TStatus")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Proforma_Machine {
  PInvNo   Int?    @default(0)
  PrdID    Int?    @default(0)
  PMdlID   Int?    @default(0)
  Qty      Int?    @default(0)
  MRP      Float?  @default(0)
  Discount Float?  @default(0)
  QtPrice  Float?  @default(0)
  TaxRate  Float?  @default(0)
  OrdWith  String? @db.VarChar(50)

  @@index([PMdlID], map: "Proforma_Machine_MdlID")
  @@index([OrdWith], map: "Proforma_Machine_OrdWithId")
  @@index([PInvNo], map: "Proforma_Machine_PInvNo")
  @@index([PrdID], map: "Proforma_Machine_PrdID")
  @@ignore
}

model Proforma_Sum {
  PInvNo    Int       @id @default(0)
  PInvDate  DateTime? @db.Timestamp(6)
  CustId    Int?      @default(0)
  RefNo     String?   @db.VarChar(50)
  DivId     Int?      @default(0)
  SegId     Int?      @default(0)
  ExecId    Int?      @default(0)
  Delay     Int?      @default(0)
  Adv       Float?    @default(0)
  DelAmt    Float?    @default(0)
  CommAmt   Float?    @default(0)
  OrderBsl  String?   @db.VarChar(150)
  OrderTapl String?   @db.VarChar(150)
  Remarks   String?   @db.VarChar(250)
  Status    String?   @db.VarChar(50)
  AuthId    Int?      @default(0)
  ForwId    Int?      @default(0)
  DelAddr   String?   @db.VarChar(255)
  Greetings String?   @db.VarChar(255)

  @@index([AuthId], map: "Proforma_Sum_AuthId")
  @@index([CustId], map: "Proforma_Sum_CustId")
  @@index([DivId], map: "Proforma_Sum_DivId")
  @@index([ExecId], map: "Proforma_Sum_ExecId")
  @@index([ForwId], map: "Proforma_Sum_ForwId")
  @@index([PInvNo], map: "Proforma_Sum_PID")
  @@index([PInvDate], map: "Proforma_Sum_QtDate")
  @@index([SegId], map: "Proforma_Sum_SegId")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model RPT_Perf {
  QtNo      Int?    @default(0)
  Part      String? @db.VarChar(50)
  TaxRate   Float?  @default(0)
  Qty       Int?    @default(0)
  Rate      Float?  @default(0)
  TaxAmt    Float?  @default(0)
  NonTaxAmt Float?  @default(0)

  @@index([QtNo], map: "RPT_Perf_TID")
  @@ignore
}

model Rel_Statistics {
  RecID    Int       @default(0)
  RecDate  DateTime  @db.Timestamp(6)
  CustID   Int?      @default(0)
  CustST   String?   @db.VarChar(2)
  MetDate  DateTime? @db.Timestamp(6)
  ExecID   Int?      @default(0)
  CPer     String?   @db.VarChar(50)
  Phone    String?   @db.VarChar(15)
  FeedBack String?   @db.VarChar(50)
  NVDate   DateTime? @db.Timestamp(6)

  @@id([RecID, RecDate])
  @@index([CustID], map: "Rel_Statistics_CustID")
  @@index([CustST], map: "Rel_Statistics_CustST")
  @@index([ExecID], map: "Rel_Statistics_ExecID")
  @@index([RecID], map: "Rel_Statistics_RelID")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model RptTmpCust {
  CustId      Int?    @default(0)
  Name        String? @db.VarChar(50)
  Particulars String? @db.VarChar(50)
  Detail      String? @db.VarChar(50)

  @@index([CustId], map: "RptTmpCust_CustId")
  @@ignore
}

model Rpt_AmcTmp {
  AmcId    Int?      @default(0)
  AssetNo  Int       @default(0)
  PrdId    Int?      @default(0)
  MdlId    Int?      @default(0)
  MCSlNo   String?   @db.VarChar(50)
  CompSlNo String?   @db.VarChar(50)
  Location String?   @db.VarChar(50)
  Wnty     DateTime? @db.Timestamp(6)
  NoMC     Int?
  HCNo     Int       @default(0)
  CompNo   Int       @default(0)
  AmcAmt   Float?    @default(0)
  BrId     Int?      @default(0)

  @@id([AssetNo, HCNo, CompNo])
  @@index([AmcId], map: "Rpt_AmcTmp_AmcId")
  @@index([BrId], map: "Rpt_AmcTmp_BrId")
  @@index([MdlId], map: "Rpt_AmcTmp_MdlId")
  @@index([PrdId], map: "Rpt_AmcTmp_PrdId")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Rpt_CRMTemp {
  SlNo           Int?    @default(0)
  Customer       String? @db.VarChar(50)
  NoOfComplaints Int?    @default(0)
  ReptOfMC       String? @db.VarChar(50)
  RptAssetNo     String? @db.VarChar(50)
  Technician     String? @db.VarChar(50)

  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Rpt_ProdTarget {
  slno        Int?    @default(0)
  Product     String? @db.VarChar(50)
  Target      Int?    @default(0)
  Billed      Int?    @default(0)
  LastBackLog Int?    @default(0)
  NetBackLog  Int?    @default(0)

  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Rpt_SPTmp {
  Slno    Int?    @default(0)
  String1 String? @db.VarChar(50)
  String2 String? @db.VarChar(50)
  Cust    String? @db.VarChar(50)
  QtMth   String? @db.VarChar(50)
  Model   String? @db.VarChar(50)
  Qty     Int?    @default(0)
  Ton     Float?  @default(0)
  Value   Float?  @default(0)
  Segment String? @db.VarChar(50)
  ExpMth  String? @db.VarChar(50)
  Prob    Int?    @default(0)
  Ideac   String? @db.VarChar(50)
  Remarks String? @db.VarChar(50)
  Rlost   String? @db.VarChar(50)
  Status  String? @db.VarChar(50)
  Enq     String? @db.VarChar(50)
  Comp    String? @db.VarChar(50)

  @@index([Ideac], map: "Rpt_SPTmp_Ideac")
  @@ignore
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model Rpt_STTmp {
  TID        Int?      @default(0)
  TStatus    String?   @db.VarChar(10)
  EntryDate  DateTime? @db.Timestamp(6)
  LogDate    DateTime? @db.Timestamp(6)
  Prospect   String?   @db.VarChar(5)
  ProbPerct  Int?      @default(0)
  FPDate     DateTime? @db.Timestamp(6)
  NVDate     DateTime? @db.Timestamp(6)
  OrdST      String?   @db.VarChar(1)
  LostReason String?   @db.VarChar(50)
  Cper       String?   @db.VarChar(50)
  Phone      String?   @db.VarChar(15)
  Rem        String?   @db.VarChar(50)
  Division   String?   @db.VarChar(50)
  USP        String?   @db.VarChar(50)
  Bias       String?   @db.VarChar(50)

  @@index([TID], map: "Rpt_STTmp_TID")
  @@index([TStatus], map: "Rpt_STTmp_TStatus")
  @@ignore
}

model SEGMENT {
  ID   Int     @id @default(0)
  Name String? @db.VarChar(50)

  @@index([ID], map: "SEGMENT_ID")
}

model SER_VISIT {
  ID       Int     @id @default(0)
  SerVisit String? @db.VarChar(50)

  @@index([ID], map: "SER_VISIT_ID")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model SETUP_DIR {
  DBDrive   String? @db.VarChar(100)
  RptDrive  String? @db.VarChar(100)
  BackDrive String? @db.VarChar(100)

  @@ignore
}

model SOMS {
  CallID      Int       @id @default(0)
  CDate       DateTime? @db.Timestamp(6)
  CustId      Int?      @default(0)
  CustST      String?   @db.VarChar(2)
  CPer        String?   @db.VarChar(50)
  CPhone      String?   @db.VarChar(15)
  PrId        Int?      @default(0)
  DownDate    DateTime? @db.Timestamp(6)
  DownTime    String?   @db.VarChar(50)
  NatSrvId    Int?      @default(0)
  UserId      Int?      @default(0)
  TechId      Int?      @default(0)
  FaultRpt    String?   @db.VarChar(250)
  OutUserId   Int?      @default(0)
  CatSrvId    Int?      @default(0)
  PartFailure String?   @db.VarChar(50)
  UpTime      String?   @db.VarChar(50)
  UpDt        DateTime? @db.Timestamp(6)
  Rect        String?   @db.VarChar(100)
  RectTechId  Int?      @default(0)
  StLevel     String?   @db.VarChar(50)
  TAT         String?   @db.VarChar(50)
  SRNo        Int?      @default(0)
  StMac       String?   @db.VarChar(50)
  FTechId     Int?      @default(0)
  Remarks     String?   @db.VarChar(50)
  InvNo       Int?      @default(0)
  Amt         Float?    @default(0)

  @@index([PartFailure], map: "SOMS_CallType")
  @@index([CatSrvId], map: "SOMS_CatSrvId")
  @@index([CustId], map: "SOMS_CustId")
  @@index([FTechId], map: "SOMS_FTechId")
  @@index([NatSrvId], map: "SOMS_NatSrvId")
  @@index([PrId], map: "SOMS_PrId")
  @@index([RectTechId], map: "SOMS_RdctTechId")
  @@index([SRNo], map: "SOMS_SRNo")
  @@index([FaultRpt], map: "SOMS_Status")
  @@index([TechId], map: "SOMS_TechId")
  @@index([UserId], map: "SOMS_UserId")
}

model SPARE {
  ID        Int     @id @default(0)
  Name      String? @db.VarChar(50)
  WtAvgCost Float?  @default(0)
  MRP       Float?  @default(0)

  @@index([ID], map: "SPARE_ID")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model ServiceRpt_CompRep {
  SRNo     Int?      @default(0)
  DefComp  String?   @db.VarChar(50)
  RpldComp String?   @db.VarChar(50)
  WDate    DateTime? @db.Timestamp(6)
  HCNo     Int?      @default(0)
  Section  String?   @db.VarChar(1)

  @@index([HCNo], map: "ServiceRpt_CompRep_HCNo")
  @@index([SRNo], map: "ServiceRpt_CompRep_SRNo1")
  @@ignore
}

model ServiceRpt_Complaint {
  SRNo   Int  @unique @default(0)
  CompID Int  @default(0)
  NatID  Int? @default(0)

  @@id([SRNo, CompID])
  @@index([CompID], map: "ServiceRpt_Complaint_CmpID")
  @@index([NatID], map: "ServiceRpt_Complaint_NatID")
}

model ServiceRpt_DebitDiv {
  SRNo       Int @default(0)
  DebitDivId Int @default(0)

  @@id([SRNo, DebitDivId])
  @@index([DebitDivId], map: "ServiceRpt_DebitDiv_DebitId")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model ServiceRpt_Expense {
  SRNo    Int?    @default(0)
  Expense String? @db.VarChar(50)
  Amt     Int?    @default(0)

  @@index([Expense], map: "ServiceRpt_Expense_CmpID")
  @@index([Amt], map: "ServiceRpt_Expense_NatID")
  @@index([SRNo], map: "ServiceRpt_Expense_SRNo")
  @@ignore
}

model ServiceRpt_Failure {
  SRNo   Int     @default(0)
  FailID Int     @default(0)
  Remedy String? @db.VarChar(50)

  @@id([SRNo, FailID])
  @@index([FailID], map: "ServiceRpt_Failure_FailID")
  @@index([SRNo], map: "ServiceRpt_Failure_SRNo")
}

model ServiceRpt_Measure {
  SRNo   Int     @id @default(0)
  SP     String? @db.VarChar(20)
  DP     String? @db.VarChar(20)
  Amp    String? @db.VarChar(20)
  Volt   String? @db.VarChar(20)
  CFM    String? @db.VarChar(20)
  Noise  String? @db.VarChar(20)
  RTemp  String? @db.VarChar(20)
  ATemp  String? @db.VarChar(20)
  TimeOn String? @db.VarChar(50)
  GCT    String? @db.VarChar(50)
}

model ServiceRpt_OrgDiv {
  SRNO     Int @default(0)
  OrgDivID Int @default(0)

  @@id([SRNO, OrgDivID])
  @@index([OrgDivID], map: "ServiceRpt_OrgDiv_OrgDivID")
}

model ServiceRpt_Spare {
  SRNo  Int    @default(0)
  SpID  Int    @default(0)
  Qty   Int?   @default(0)
  Price Float? @default(0)

  @@id([SRNo, SpID])
}

model ServiceRpt_Visit {
  SRNo       Int @default(0)
  SerVisitID Int @default(0)

  @@id([SRNo, SerVisitID])
  @@index([SerVisitID], map: "ServiceRpt_Visit_VisitID")
}

model ServiceRpt_sum {
  SRNo     Int       @default(0)
  SRDate   DateTime? @db.Timestamp(6)
  HCNo     Int       @default(0)
  AssetNo  Int       @default(0)
  FirstRes DateTime? @db.Timestamp(6)
  WorkDate DateTime? @db.Timestamp(6)
  RmDate   DateTime? @db.Timestamp(6)
  RetDate  DateTime? @db.Timestamp(6)
  LstSRNo  Int?      @default(0)
  Item     String?   @db.VarChar(20)
  Fault    String?   @db.VarChar(100)
  Action   String?   @db.VarChar(100)
  FollowUp String?   @db.VarChar(100)
  ServID   Int?      @default(0)

  @@id([HCNo, AssetNo, SRNo])
  @@index([SRNo], map: "ServiceRpt_sum_SRNo")
  @@index([ServID], map: "ServiceRpt_sum_ServID")
}

model SpareCost {
  PurNo    String    @db.VarChar(50)
  PDate    DateTime? @db.Timestamp(6)
  SpID     Int       @default(0)
  Qty      Int?      @default(0)
  Amt      Float?    @default(0)
  UnitCost Float?    @default(0)

  @@id([PurNo, SpID])
  @@index([SpID], map: "SpareCost_SpID")
}

model TAX {
  TaxId   Int    @id @default(0)
  TaxRate Float? @default(0)

  @@index([TaxId], map: "TAX_TxId")
}

model TERRITORY {
  TerrId Int     @id @default(0)
  Terr   String? @db.VarChar(50)

  @@index([TerrId], map: "TERRITORY_TerrId")
}

model TRANSIT_DAMAGE {
  TrID     Int     @id @default(0)
  TrDamage String? @db.VarChar(50)

  @@index([TrID], map: "TRANSIT_DAMAGE_TrId")
}

model Transaction {
  TID         Int     @default(0)
  TStatus     String  @db.VarChar(1)
  CustID      Int?    @default(0)
  ExecID      Int?    @default(0)
  TerrId      Int     @default(0)
  SegID       Int?    @default(0)
  EnqID       Int?    @default(0)
  VTID        Int?    @default(0)
  STFlag      String? @db.VarChar(1)
  CustSt      String  @db.VarChar(2)
  Ideac       String? @db.VarChar(50)
  QtNo        Int
  TimeElapsed Int?    @default(0)

  @@id([TID, TStatus])
  @@index([CustID], map: "Transaction_CustID")
  @@index([CustSt], map: "Transaction_CustSt")
  @@index([EnqID], map: "Transaction_EnqID")
  @@index([ExecID], map: "Transaction_ExecID")
  @@index([Ideac], map: "Transaction_Ideac")
  @@index([QtNo], map: "Transaction_QtNo")
  @@index([SegID], map: "Transaction_SegID")
  @@index([TID], map: "Transaction_TID")
  @@index([TStatus], map: "Transaction_TStatus")
  @@index([TerrId], map: "Transaction_Terr")
  @@index([VTID], map: "Transaction_VTID")
}

model Transaction_Bias {
  TID    Int @default(0)
  BiasID Int @default(0)

  @@id([TID, BiasID])
  @@index([BiasID], map: "Transaction_Bias_BiasID")
  @@index([TID], map: "Transaction_Bias_FID")
}

model Transaction_Machine {
  TID         Int     @default(0)
  PrdID       Int     @default(0)
  MdlID       Int     @default(0)
  Qty         Int?    @default(0)
  QtPrice     Float?  @default(0)
  TaxRate     Float?  @default(0)
  SalesMargin Float?  @default(0)
  BlStar      String? @default("0") @db.VarChar(1)

  @@id([TID, PrdID, MdlID])
  @@index([MdlID], map: "Transaction_Machine_MdlID")
  @@index([PrdID], map: "Transaction_Machine_PrdID")
  @@index([TID], map: "Transaction_Machine_TID")
}

model Transaction_USP {
  TID   Int @default(0)
  USPID Int @default(0)

  @@id([TID, USPID])
  @@index([TID], map: "Transaction_USP_FID")
  @@index([USPID], map: "Transaction_USP_USPID")
}

model USERGROUP {
  UserGrpId Int     @id @default(0)
  Name      String? @db.VarChar(50)

  @@index([UserGrpId], map: "USERGROUP_NatSrvId")
}

model USP {
  ID   Int     @id @default(0)
  Name String? @db.VarChar(50)

  @@index([ID], map: "USP_ID")
}

model UserPwd {
  UserID Int     @id @default(0)
  User   String? @db.VarChar(50)
  Pwd    String? @db.VarChar(50)
  Key    String? @db.VarChar(50)

  @@index([Key], map: "UserPwd_Key")
  @@index([UserID], map: "UserPwd_UserID")
}

model VISIT {
  ID   Int     @id @default(0)
  Name String? @db.VarChar(50)

  @@index([ID], map: "VISIT_ID")
}

model out_warranties {
  id                 String                    @id @default(uuid())
  customerId         String                    @map("customer_id")
  executiveId        String                    @map("executive_id")
  contactPerson      String?
  contactPhone       String?
  startDate          DateTime                  @map("start_date")
  endDate            DateTime                  @map("end_date")
  amount             Decimal                   @map("amount") @db.Decimal(10, 2)
  source             String                    @default("NEW")
  sourceId           String?                   @map("source_id")
  amcSourceId        String?                   @unique @map("amc_source_id")
  inWarrantySourceId String?                   @unique @map("in_warranty_source_id")
  isActive           Boolean                   @default(true) @map("is_active")
  originalId         Int?                      @map("original_id")
  createdAt          DateTime                  @default(now()) @map("created_at")
  updatedAt          DateTime                  @updatedAt @map("updated_at")
  historyCards       history_cards[]
  amcContract        amc_contracts?            @relation(fields: [amcSourceId], references: [id])
  customer           Customer                  @relation(fields: [customerId], references: [id])
  executive          users                     @relation(fields: [executiveId], references: [id])
  components         out_warranty_components[]
  machines           out_warranty_machines[]
  payments           out_warranty_payments[]

  @@index([customerId])
  @@index([executiveId])
  @@index([startDate, endDate])
  @@index([originalId])
}

model out_warranty_machines {
  id                    String                    @id @default(uuid())
  outWarrantyId         String                    @map("out_warranty_id")
  modelId               String                    @map("model_id")
  serialNumber          String                    @map("serial_number")
  location              String?
  originalOutWarrantyId Int?                      @map("original_out_warranty_id")
  originalAssetNo       Int?                      @map("original_asset_no")
  originalHistoryCardNo Int?                      @map("original_history_card_no")
  createdAt             DateTime                  @default(now()) @map("created_at")
  updatedAt             DateTime                  @updatedAt @map("updated_at")
  components            out_warranty_components[]
  model                 models                    @relation(fields: [modelId], references: [id])
  outWarranty           out_warranties            @relation(fields: [outWarrantyId], references: [id], onDelete: Cascade)

  @@unique([outWarrantyId, serialNumber])
  @@index([outWarrantyId])
  @@index([modelId])
  @@index([originalOutWarrantyId, originalAssetNo, originalHistoryCardNo])
}

model out_warranty_components {
  id                    String                @id @default(uuid())
  machineId             String                @map("machine_id")
  type                  String
  serialNumber          String                @map("serial_number")
  originalOutWarrantyId Int?                  @map("original_out_warranty_id")
  originalAssetNo       Int?                  @map("original_asset_no")
  originalComponentNo   Int?                  @map("original_component_no")
  createdAt             DateTime              @default(now()) @map("created_at")
  updatedAt             DateTime              @updatedAt @map("updated_at")
  out_warrantiesId      String?
  machine               out_warranty_machines @relation(fields: [machineId], references: [id], onDelete: Cascade)
  out_warranties        out_warranties?       @relation(fields: [out_warrantiesId], references: [id])

  @@unique([machineId, type, serialNumber])
  @@index([machineId])
  @@index([originalOutWarrantyId, originalAssetNo, originalComponentNo])
}

model out_warranty_payments {
  id                    String         @id @default(uuid())
  outWarrantyId         String         @map("out_warranty_id")
  receiptNo             String?        @map("receipt_no")
  paymentDate           DateTime       @map("payment_date")
  paymentMode           String?        @map("payment_mode")
  amount                Float
  particulars           String?
  originalOutWarrantyId Int?           @map("original_out_warranty_id")
  originalReceiptNo     Int?           @map("original_receipt_no")
  createdAt             DateTime       @default(now()) @map("created_at")
  updatedAt             DateTime       @updatedAt @map("updated_at")
  outWarranty           out_warranties @relation(fields: [outWarrantyId], references: [id], onDelete: Cascade)

  @@index([outWarrantyId])
  @@index([receiptNo])
  @@index([paymentDate])
  @@index([originalOutWarrantyId, originalReceiptNo])
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model service_reports {
  id              String              @id @default(uuid())
  customerId      String              @map("customer_id")
  executiveId     String              @map("executive_id")
  reportDate      DateTime            @map("report_date")
  visitDate       DateTime?           @map("visit_date")
  completionDate  DateTime?           @map("completion_date")
  natureOfService String?             @map("nature_of_service")
  complaintType   String?             @map("complaint_type")
  actionTaken     String?             @map("action_taken")
  remarks         String?
  status          String              @default("OPEN")
  originalId      Int?                @map("original_id")
  createdAt       DateTime            @default(now()) @map("created_at")
  updatedAt       DateTime            @updatedAt @map("updated_at")
  details         service_details[]
  schedules       service_schedules[]
  customer        Customer            @relation(fields: [customerId], references: [id])
  executive       users               @relation(fields: [executiveId], references: [id])

  @@index([customerId])
  @@index([executiveId])
  @@index([reportDate])
  @@index([visitDate])
  @@index([status])
  @@index([originalId])
  @@index([completionDate], map: "idx_service_reports_completion_date")
  @@index([reportDate], map: "idx_service_reports_report_date")
  @@index([status], map: "idx_service_reports_status")
  @@index([visitDate], map: "idx_service_reports_visit_date")
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model service_details {
  id              String          @id @default(uuid())
  serviceReportId String          @map("service_report_id")
  machineType     String?         @map("machine_type")
  serialNumber    String?         @map("serial_number")
  problem         String?
  solution        String?
  partReplaced    String?         @map("part_replaced")
  createdAt       DateTime        @default(now()) @map("created_at")
  updatedAt       DateTime        @updatedAt @map("updated_at")
  serviceReport   service_reports @relation(fields: [serviceReportId], references: [id], onDelete: Cascade)

  @@index([serviceReportId])
  @@index([serialNumber])
  @@index([serialNumber], map: "idx_service_details_serial_number")
}

model service_schedules {
  id                String          @id @default(uuid())
  serviceReportId   String          @map("service_report_id")
  scheduledDate     DateTime        @map("scheduled_date")
  technicianId      String?         @map("technician_id")
  estimatedDuration Int?            @map("estimated_duration") // Duration in minutes
  priority          String          @default("MEDIUM") // LOW, MEDIUM, HIGH, URGENT
  notes             String?
  status            String          @default("SCHEDULED") // SCHEDULED, IN_PROGRESS, COMPLETED, CANCELLED
  createdAt         DateTime        @default(now()) @map("created_at")
  updatedAt         DateTime        @updatedAt @map("updated_at")
  serviceReport     service_reports @relation(fields: [serviceReportId], references: [id], onDelete: Cascade)
  technician        users?          @relation(fields: [technicianId], references: [id])

  @@index([serviceReportId])
  @@index([scheduledDate])
  @@index([technicianId])
  @@index([status])
  @@index([priority])
}

model history_cards {
  id                    String                        @id @default(uuid())
  customerId            String                        @map("customer_id")
  cardNo                Int?                          @map("card_no")
  source                String?
  amcId                 String?                       @map("amc_id")
  inWarrantyId          String?                       @map("in_warranty_id")
  outWarrantyId         String?                       @map("out_warranty_id")
  toCardNo              Int?                          @map("to_card_no")
  originalId            Int?                          @map("original_id")
  createdAt             DateTime                      @default(now()) @map("created_at")
  updatedAt             DateTime                      @updatedAt @map("updated_at")
  addonDetails          HistoryAddonDetail[]
  amcDetails            HistoryAmcDetail[]
  audits                HistoryAudit[]
  amcContract           amc_contracts?                @relation(fields: [amcId], references: [id])
  customer              Customer                      @relation(fields: [customerId], references: [id])
  inWarranty            warranties?                   @relation(fields: [inWarrantyId], references: [id])
  outWarranty           out_warranties?               @relation(fields: [outWarrantyId], references: [id])
  complaints            HistoryComplaint[]
  componentReplacements HistoryComponentReplacement[]
  maintenance           HistoryMaintenance[]
  repairs               HistoryRepair[]
  sections              history_sections[]
  waterWashes           HistoryWaterWash[]

  @@index([customerId])
  @@index([cardNo])
  @@index([amcId])
  @@index([inWarrantyId])
  @@index([outWarrantyId])
  @@index([originalId])
  @@index([cardNo], map: "idx_history_cards_card_no")
  @@index([source], map: "idx_history_cards_source")
}

model history_sections {
  id                  String        @id @default(uuid())
  historyCardId       String        @map("history_card_id")
  sectionCode         String        @map("section_code")
  content             String
  originalCardNo      Int?          @map("original_card_no")
  originalSectionCode String?       @map("original_section_code")
  createdAt           DateTime      @default(now()) @map("created_at")
  updatedAt           DateTime      @updatedAt @map("updated_at")
  historyCard         history_cards @relation(fields: [historyCardId], references: [id], onDelete: Cascade)

  @@unique([historyCardId, sectionCode])
  @@index([historyCardId])
  @@index([originalCardNo, originalSectionCode])
}

model sales_leads {
  id                 String    @id @default(uuid())
  customerId         String    @map("customer_id")
  executiveId        String    @map("executive_id")
  leadDate           DateTime  @map("lead_date")
  contactPerson      String?   @map("contact_person")
  contactPhone       String?   @map("contact_phone")
  status             String    @default("NEW")
  prospectPercentage Int?      @map("prospect_percentage")
  followUpDate       DateTime? @map("follow_up_date")
  nextVisitDate      DateTime? @map("next_visit_date")
  remarks            String?
  originalId         Int?      @map("original_id")
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  customer           Customer  @relation(fields: [customerId], references: [id])
  executive          users     @relation(fields: [executiveId], references: [id])

  @@index([customerId])
  @@index([executiveId])
  @@index([leadDate])
  @@index([status])
  @@index([followUpDate])
  @@index([originalId])
}

model sales_opportunities {
  id                 String    @id @default(uuid())
  customerId         String    @map("customer_id")
  executiveId        String    @map("executive_id")
  opportunityDate    DateTime  @map("opportunity_date")
  contactPerson      String?   @map("contact_person")
  contactPhone       String?   @map("contact_phone")
  status             String    @default("OPEN")
  prospectPercentage Int?      @map("prospect_percentage")
  followUpDate       DateTime? @map("follow_up_date")
  nextVisitDate      DateTime? @map("next_visit_date")
  remarks            String?
  originalId         Int?      @map("original_id")
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  customer           Customer  @relation(fields: [customerId], references: [id])
  executive          users     @relation(fields: [executiveId], references: [id])

  @@index([customerId])
  @@index([executiveId])
  @@index([opportunityDate])
  @@index([status])
  @@index([followUpDate])
  @@index([originalId])
}

model sales_prospects {
  id                 String    @id @default(uuid())
  customerId         String    @map("customer_id")
  executiveId        String    @map("executive_id")
  prospectDate       DateTime  @map("prospect_date")
  contactPerson      String?   @map("contact_person")
  contactPhone       String?   @map("contact_phone")
  status             String    @default("ACTIVE")
  prospectPercentage Int?      @map("prospect_percentage")
  followUpDate       DateTime? @map("follow_up_date")
  nextVisitDate      DateTime? @map("next_visit_date")
  lostReason         String?   @map("lost_reason")
  remarks            String?
  originalId         Int?      @map("original_id")
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  customer           Customer  @relation(fields: [customerId], references: [id])
  executive          users     @relation(fields: [executiveId], references: [id])

  @@index([customerId])
  @@index([executiveId])
  @@index([prospectDate])
  @@index([status])
  @@index([followUpDate])
  @@index([originalId])
}

model sales_orders {
  id                 String    @id @default(uuid())
  customerId         String    @map("customer_id")
  executiveId        String    @map("executive_id")
  orderDate          DateTime  @map("order_date")
  contactPerson      String?   @map("contact_person")
  contactPhone       String?   @map("contact_phone")
  status             String    @default("PENDING")
  deliveryDate       DateTime? @map("delivery_date")
  actualDeliveryDate DateTime? @map("actual_delivery_date")
  amount             Float
  remarks            String?
  originalId         Int?      @map("original_id")
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  customer           Customer  @relation(fields: [customerId], references: [id])
  executive          users     @relation(fields: [executiveId], references: [id])

  @@index([customerId])
  @@index([executiveId])
  @@index([orderDate])
  @@index([status])
  @@index([deliveryDate])
  @@index([originalId])
}

model users {
  id                                 String                     @id @default(uuid())
  name                               String
  email                              String                     @unique
  password                           String
  role                               String                     @default("user")
  phone                              String?
  designation                        String?
  isActive                           Boolean                    @default(true) @map("is_active")
  originalId                         Int?                       @map("original_id")
  createdAt                          DateTime                   @default(now()) @map("created_at")
  updatedAt                          DateTime                   @updatedAt @map("updated_at")
  emailVerified                      DateTime?                  @map("email_verified") @db.Timestamp(6)
  image                              String?
  accounts                           Account[]
  activityLogs                       activity_logs[]
  amcContracts                       amc_contracts[]
  outWarranties                      out_warranties[]
  passwordResetToken                 PasswordResetToken?
  salesLeads                         sales_leads[]
  salesOpportunities                 sales_opportunities[]
  salesOrders                        sales_orders[]
  salesProspects                     sales_prospects[]
  serviceReports                     service_reports[]
  serviceSchedules                   service_schedules[]
  sessions                           Session[]
  warranties                         warranties[]
  quotations                         quotations[]
  notificationPreferences            NotificationPreference?
  salesNotificationEvents            SalesNotificationEvent[]
  salesNotificationEventsAsExecutive SalesNotificationEvent[]   @relation("SalesNotificationEventExecutive")
  salesNotificationQueueAsRecipient  SalesNotificationQueue[]   @relation("SalesNotificationQueueRecipient")
  scheduledReports                   scheduled_reports[]
  reportFormulas                     report_formulas[]
  emailDistributionLists             email_distribution_lists[]
  reportEmailConfigs                 report_email_configs[]
  reportEmailDeliveries              report_email_deliveries[]

  @@index([name])
  @@index([email])
  @@index([role])
  @@index([originalId])
  @@index([isActive], map: "idx_users_is_active")
  @@index([role], map: "idx_users_role")
}

model PasswordResetToken {
  id        String   @id @default(uuid())
  userId    String   @unique @map("user_id")
  token     String   @unique
  expires   DateTime
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  user      users    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([token])
  @@map("password_reset_tokens")
}

model ServiceVisitType {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("service_visit_types")
}

model ComplaintType {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("complaint_types")
}

model ComplaintNatureType {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("complaint_nature_types")
}

model FailureType {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("failure_types")
}

model Territory {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("territories")
}

model Segment {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("segments")
}

model Competitor {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("competitors")
}

model PriorityType {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("priority_types")
}

model EnquiryType {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("enquiry_types")
}

model DeductionType {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("deduction_types")
}

model DebitDivision {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("debit_divisions")
}

model AccountDivision {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("account_divisions")
}

model SparePart {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("spare_parts")
}

model TaxRate {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("tax_rates")
}

model TransitDamageType {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("transit_damage_types")
}

model UserGroup {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("user_groups")
}

model UspType {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("usp_types")
}

model VisitType {
  id          String   @id @default(uuid())
  name        String
  description String?
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([originalId])
  @@map("visit_types")
}

model ServiceCenter {
  id            String   @id @default(uuid())
  name          String
  vendor        String? // e.g., 'BLUESTAR', 'GENERAL'
  address       String?
  city          String?
  state         String?
  pincode       String?
  phone         String?
  email         String?
  contactPerson String?  @map("contact_person")
  active        Boolean  @default(true)
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([vendor])
  @@index([city])
  @@index([active])
  @@map("service_centers")
}

model HistoryRepair {
  id               String        @id @default(uuid())
  historyCardId    String        @map("history_card_id")
  repairDate       DateTime?     @map("repair_date")
  description      String
  technicianId     String?       @map("technician_id")
  partsReplaced    String?       @map("parts_replaced")
  cost             Decimal?      @db.Decimal(10, 2)
  originalCardNo   Int?          @map("original_card_no")
  originalRepairId Int?          @map("original_repair_id")
  createdAt        DateTime      @default(now()) @map("created_at")
  updatedAt        DateTime      @updatedAt @map("updated_at")
  historyCard      history_cards @relation(fields: [historyCardId], references: [id], onDelete: Cascade)

  @@index([historyCardId])
  @@index([repairDate])
  @@index([originalCardNo])
  @@map("history_repairs")
}

model HistoryMaintenance {
  id                    String        @id @default(uuid())
  historyCardId         String        @map("history_card_id")
  maintenanceDate       DateTime?     @map("maintenance_date")
  description           String
  technicianId          String?       @map("technician_id")
  maintenanceType       String?       @map("maintenance_type")
  originalCardNo        Int?          @map("original_card_no")
  originalMaintenanceId Int?          @map("original_maintenance_id")
  createdAt             DateTime      @default(now()) @map("created_at")
  updatedAt             DateTime      @updatedAt @map("updated_at")
  historyCard           history_cards @relation(fields: [historyCardId], references: [id], onDelete: Cascade)

  @@index([historyCardId])
  @@index([maintenanceDate])
  @@index([originalCardNo])
  @@map("history_maintenance")
}

model HistoryWaterWash {
  id             String        @id @default(uuid())
  historyCardId  String        @map("history_card_id")
  washDate       DateTime?     @map("wash_date")
  description    String
  technicianId   String?       @map("technician_id")
  originalCardNo Int?          @map("original_card_no")
  originalWashId Int?          @map("original_wash_id")
  createdAt      DateTime      @default(now()) @map("created_at")
  updatedAt      DateTime      @updatedAt @map("updated_at")
  historyCard    history_cards @relation(fields: [historyCardId], references: [id], onDelete: Cascade)

  @@index([historyCardId])
  @@index([washDate])
  @@index([originalCardNo])
  @@map("history_water_washes")
}

model HistoryAmcDetail {
  id                  String         @id @default(uuid())
  historyCardId       String         @map("history_card_id")
  amcContractId       String?        @map("amc_contract_id")
  startDate           DateTime?      @map("start_date")
  endDate             DateTime?      @map("end_date")
  amount              Decimal?       @db.Decimal(10, 2)
  description         String
  originalCardNo      Int?           @map("original_card_no")
  createdAt           DateTime       @default(now()) @map("created_at")
  updatedAt           DateTime       @updatedAt @map("updated_at")
  originalAmcDetailId Int?           @map("original_amc_detail_id")
  amcContract         amc_contracts? @relation(fields: [amcContractId], references: [id])
  historyCard         history_cards  @relation(fields: [historyCardId], references: [id], onDelete: Cascade)

  @@index([historyCardId])
  @@index([amcContractId])
  @@index([startDate])
  @@index([endDate])
  @@index([originalCardNo])
  @@map("history_amc_details")
}

model HistoryAddonDetail {
  id                    String        @id @default(uuid())
  historyCardId         String        @map("history_card_id")
  addonDate             DateTime?     @map("addon_date")
  description           String
  amount                Decimal?      @db.Decimal(10, 2)
  technicianId          String?       @map("technician_id")
  originalCardNo        Int?          @map("original_card_no")
  createdAt             DateTime      @default(now()) @map("created_at")
  updatedAt             DateTime      @updatedAt @map("updated_at")
  originalAddonDetailId Int?          @map("original_addon_detail_id")
  historyCard           history_cards @relation(fields: [historyCardId], references: [id], onDelete: Cascade)

  @@index([historyCardId])
  @@index([addonDate])
  @@index([originalCardNo])
  @@map("history_addon_details")
}

model HistoryAudit {
  id              String        @id @default(uuid())
  historyCardId   String        @map("history_card_id")
  auditDate       DateTime?     @map("audit_date")
  description     String
  auditorId       String?       @map("auditor_id")
  findings        String?
  recommendations String?
  originalCardNo  Int?          @map("original_card_no")
  createdAt       DateTime      @default(now()) @map("created_at")
  updatedAt       DateTime      @updatedAt @map("updated_at")
  originalAuditId Int?          @map("original_audit_id")
  historyCard     history_cards @relation(fields: [historyCardId], references: [id], onDelete: Cascade)

  @@index([historyCardId])
  @@index([auditDate])
  @@index([originalCardNo])
  @@map("history_audits")
}

model HistoryComplaint {
  id                  String        @id @default(uuid())
  historyCardId       String        @map("history_card_id")
  complaintDate       DateTime?     @map("complaint_date")
  description         String
  complaintType       String?       @map("complaint_type")
  resolution          String?
  resolutionDate      DateTime?     @map("resolution_date")
  originalCardNo      Int?          @map("original_card_no")
  createdAt           DateTime      @default(now()) @map("created_at")
  updatedAt           DateTime      @updatedAt @map("updated_at")
  originalComplaintId Int?          @map("original_complaint_id")
  historyCard         history_cards @relation(fields: [historyCardId], references: [id], onDelete: Cascade)

  @@index([historyCardId])
  @@index([complaintDate])
  @@index([resolutionDate])
  @@index([originalCardNo])
  @@map("history_complaints")
}

model HistoryComponentReplacement {
  id                    String        @id @default(uuid())
  historyCardId         String        @map("history_card_id")
  replacementDate       DateTime?     @map("replacement_date")
  componentName         String        @map("component_name")
  serialNumber          String?       @map("serial_number")
  reason                String?
  technicianId          String?       @map("technician_id")
  cost                  Decimal?      @db.Decimal(10, 2)
  originalCardNo        Int?          @map("original_card_no")
  createdAt             DateTime      @default(now()) @map("created_at")
  updatedAt             DateTime      @updatedAt @map("updated_at")
  originalReplacementId Int?          @map("original_replacement_id")
  historyCard           history_cards @relation(fields: [historyCardId], references: [id], onDelete: Cascade)

  @@index([historyCardId])
  @@index([replacementDate])
  @@index([serialNumber])
  @@index([originalCardNo])
  @@map("history_component_replacements")
}

model EmailTemplate {
  id                 String                 @id @default(uuid())
  name               String                 @unique
  subject            String
  bodyHtml           String                 @map("body_html")
  bodyText           String                 @map("body_text")
  description        String?
  variables          String[]
  category           String?
  isActive           Boolean                @default(true) @map("is_active")
  createdAt          DateTime               @default(now()) @map("created_at")
  updatedAt          DateTime               @updatedAt @map("updated_at")
  emailLogs          EmailLog[]
  reportEmailConfigs report_email_configs[]

  @@index([name])
  @@index([category])
  @@index([isActive])
  @@map("email_templates")
}

// Notification Preferences Model
model NotificationPreference {
  id                            String   @id @default(uuid())
  userId                        String   @map("user_id")
  salesLeadCreated              Boolean  @default(true) @map("sales_lead_created")
  salesLeadStatusChanged        Boolean  @default(true) @map("sales_lead_status_changed")
  salesOpportunityCreated       Boolean  @default(true) @map("sales_opportunity_created")
  salesOpportunityStatusChanged Boolean  @default(true) @map("sales_opportunity_status_changed")
  salesProspectCreated          Boolean  @default(true) @map("sales_prospect_created")
  salesProspectStatusChanged    Boolean  @default(true) @map("sales_prospect_status_changed")
  salesOrderCreated             Boolean  @default(true) @map("sales_order_created")
  salesOrderStatusChanged       Boolean  @default(true) @map("sales_order_status_changed")
  salesConversionEvents         Boolean  @default(true) @map("sales_conversion_events")
  dailySalesSummary             Boolean  @default(false) @map("daily_sales_summary")
  weeklySalesReport             Boolean  @default(false) @map("weekly_sales_report")
  isActive                      Boolean  @default(true) @map("is_active")
  createdAt                     DateTime @default(now()) @map("created_at")
  updatedAt                     DateTime @updatedAt @map("updated_at")

  user users @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId])
  @@index([userId])
  @@index([isActive])
  @@map("notification_preferences")
}

// Sales Notification Events Model
model SalesNotificationEvent {
  id          String    @id @default(uuid())
  eventType   String    @map("event_type") // 'LEAD_CREATED', 'LEAD_STATUS_CHANGED', etc.
  entityType  String    @map("entity_type") // 'lead', 'opportunity', 'prospect', 'order'
  entityId    String    @map("entity_id")
  userId      String?   @map("user_id") // User who triggered the event
  customerId  String?   @map("customer_id")
  executiveId String?   @map("executive_id")
  oldStatus   String?   @map("old_status")
  newStatus   String?   @map("new_status")
  eventData   Json?     @map("event_data") // Additional event-specific data
  processed   Boolean   @default(false)
  processedAt DateTime? @map("processed_at")
  createdAt   DateTime  @default(now()) @map("created_at")

  user       users?                   @relation(fields: [userId], references: [id], onDelete: SetNull)
  customer   Customer?                @relation(fields: [customerId], references: [id], onDelete: SetNull)
  executive  users?                   @relation("SalesNotificationEventExecutive", fields: [executiveId], references: [id], onDelete: SetNull)
  queueItems SalesNotificationQueue[]

  @@index([eventType])
  @@index([entityType])
  @@index([entityId])
  @@index([processed])
  @@index([createdAt])
  @@index([userId])
  @@index([customerId])
  @@index([executiveId])
  @@map("sales_notification_events")
}

// Sales Notification Queue Model
model SalesNotificationQueue {
  id              String    @id @default(uuid())
  eventId         String    @map("event_id")
  recipientUserId String    @map("recipient_user_id")
  recipientEmail  String    @map("recipient_email")
  templateName    String    @map("template_name")
  templateData    Json      @map("template_data")
  priority        String    @default("NORMAL") // 'HIGH', 'NORMAL', 'LOW'
  status          String    @default("PENDING") // 'PENDING', 'PROCESSING', 'SENT', 'FAILED', 'CANCELLED'
  attempts        Int       @default(0)
  maxAttempts     Int       @default(3) @map("max_attempts")
  lastAttemptAt   DateTime? @map("last_attempt_at")
  sentAt          DateTime? @map("sent_at")
  failureReason   String?   @map("failure_reason")
  emailLogId      String?   @map("email_log_id")
  scheduledFor    DateTime? @map("scheduled_for")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  event         SalesNotificationEvent @relation(fields: [eventId], references: [id], onDelete: Cascade)
  recipientUser users                  @relation("SalesNotificationQueueRecipient", fields: [recipientUserId], references: [id], onDelete: Cascade)
  emailLog      EmailLog?              @relation(fields: [emailLogId], references: [id], onDelete: SetNull)

  @@index([eventId])
  @@index([recipientUserId])
  @@index([status])
  @@index([priority])
  @@index([scheduledFor])
  @@index([createdAt])
  @@map("sales_notification_queue")
}

model quotations {
  id              String    @id @default(uuid())
  quotationNumber String    @unique @map("quotation_number")
  customerId      String    @map("customer_id")
  executiveId     String    @map("executive_id")
  quotationDate   DateTime  @map("quotation_date")
  validUntil      DateTime? @map("valid_until")
  status          String    @default("DRAFT") // DRAFT, SENT, ACCEPTED, REJECTED, EXPIRED
  contactPerson   String?   @map("contact_person")
  contactPhone    String?   @map("contact_phone")
  contactEmail    String?   @map("contact_email")
  subject         String?
  notes           String?
  termsConditions String?   @map("terms_conditions")
  subtotal        Float     @default(0)
  taxAmount       Float     @default(0) @map("tax_amount")
  totalAmount     Float     @default(0) @map("total_amount")
  discount        Float?    @default(0)
  discountType    String?   @default("PERCENTAGE") @map("discount_type") // PERCENTAGE, FIXED
  originalId      Int?      @map("original_id")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  // Relations
  customer  Customer          @relation(fields: [customerId], references: [id])
  executive users             @relation(fields: [executiveId], references: [id])
  items     quotation_items[]

  @@index([customerId])
  @@index([executiveId])
  @@index([quotationDate])
  @@index([status])
  @@index([quotationNumber])
}

model quotation_items {
  id             String   @id @default(uuid())
  quotationId    String   @map("quotation_id")
  productId      String?  @map("product_id")
  modelId        String?  @map("model_id")
  brandId        String?  @map("brand_id")
  description    String
  quantity       Int      @default(1)
  unitPrice      Float    @map("unit_price")
  totalPrice     Float    @map("total_price")
  taxRate        Float?   @default(0) @map("tax_rate")
  taxAmount      Float?   @default(0) @map("tax_amount")
  discount       Float?   @default(0)
  discountType   String?  @default("PERCENTAGE") @map("discount_type") // PERCENTAGE, FIXED
  specifications String?
  notes          String?
  sortOrder      Int?     @default(0) @map("sort_order")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  quotation quotations @relation(fields: [quotationId], references: [id], onDelete: Cascade)
  product   products?  @relation(fields: [productId], references: [id])
  model     models?    @relation(fields: [modelId], references: [id])
  brand     brands?    @relation(fields: [brandId], references: [id])

  @@index([quotationId])
  @@index([productId])
  @@index([modelId])
  @@index([sortOrder])
}

model EmailLog {
  id                     String                   @id @default(uuid())
  templateId             String?                  @map("template_id")
  recipient              String
  subject                String
  bodyHtml               String?                  @map("body_html")
  bodyText               String?                  @map("body_text")
  cc                     String[]
  bcc                    String[]
  status                 String
  errorMessage           String?                  @map("error_message")
  sentAt                 DateTime?                @map("sent_at")
  createdAt              DateTime                 @default(now()) @map("created_at")
  updatedAt              DateTime                 @updatedAt @map("updated_at")
  template               EmailTemplate?           @relation(fields: [templateId], references: [id])
  salesNotificationQueue SalesNotificationQueue[]

  @@index([templateId])
  @@index([recipient])
  @@index([status])
  @@index([sentAt])
  @@map("email_logs")
}

model activity_logs {
  id         String   @id @default(uuid())
  userId     String?  @map("user_id")
  action     String
  entityType String?  @map("entity_type")
  entityId   String?  @map("entity_id")
  details    Json?
  ipAddress  String?  @map("ip_address")
  userAgent  String?  @map("user_agent")
  createdAt  DateTime @default(now()) @map("created_at")
  user       users?   @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([action])
  @@index([entityType, entityId])
  @@index([createdAt])
  @@map("activity_logs")
}

model Account {
  id                String  @id @default(cuid())
  userId            String  @map("user_id")
  type              String
  provider          String
  providerAccountId String  @map("provider_account_id")
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              users   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("session_token")
  userId       String   @map("user_id")
  expires      DateTime
  user         users    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model SpareType {
  id          String   @id @default(uuid())
  name        String
  description String?
  code        String?
  active      Boolean  @default(true)
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([code])
  @@index([active])
  @@index([originalId])
  @@map("spare_types")
}

model MeasurementType {
  id          String   @id @default(uuid())
  name        String
  description String?
  code        String?
  active      Boolean  @default(true)
  originalId  Int?     @map("original_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@index([name])
  @@index([code])
  @@index([active])
  @@index([originalId])
  @@map("measurement_types")
}

// Report Scheduling Tables
model scheduled_reports {
  id              String    @id @default(uuid())
  name            String // User-friendly name for the schedule
  description     String? // Optional description
  reportType      String    @map("report_type") // AMC, WARRANTY, SERVICE, SALES, CUSTOMER
  cronExpression  String    @map("cron_expression") // Cron expression for scheduling
  isActive        Boolean   @default(true) @map("is_active")
  parameters      Json? // Report parameters as JSON
  emailRecipients String[]  @map("email_recipients") // Array of email addresses
  emailSubject    String?   @map("email_subject") // Custom email subject
  emailBody       String?   @map("email_body") // Custom email body
  exportFormat    String    @default("PDF") @map("export_format") // PDF, EXCEL, CSV
  createdBy       String    @map("created_by")
  lastRunAt       DateTime? @map("last_run_at")
  nextRunAt       DateTime? @map("next_run_at")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  // Relations
  creator    users                         @relation(fields: [createdBy], references: [id])
  executions scheduled_report_executions[]

  @@index([reportType])
  @@index([isActive])
  @@index([nextRunAt])
  @@index([createdBy])
  @@map("scheduled_reports")
}

model scheduled_report_executions {
  id                String    @id @default(uuid())
  scheduledReportId String    @map("scheduled_report_id")
  status            String    @default("PENDING") // PENDING, RUNNING, COMPLETED, FAILED
  startedAt         DateTime? @map("started_at")
  completedAt       DateTime? @map("completed_at")
  errorMessage      String?   @map("error_message")
  reportData        Json?     @map("report_data") // Generated report metadata
  filePath          String?   @map("file_path") // Path to generated file
  emailsSent        Int       @default(0) @map("emails_sent")
  emailErrors       String[]  @map("email_errors") // Array of email error messages
  executionTime     Int?      @map("execution_time") // Execution time in milliseconds
  recordCount       Int?      @map("record_count") // Number of records in report
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")

  // Relations
  scheduledReport scheduled_reports @relation(fields: [scheduledReportId], references: [id], onDelete: Cascade)

  @@index([scheduledReportId])
  @@index([status])
  @@index([startedAt])
  @@map("scheduled_report_executions")
}

// Formula Engine Tables
model report_formulas {
  id          String    @id @default(uuid())
  name        String // User-friendly name for the formula
  description String? // Optional description
  formula     String // The formula expression
  category    String    @default("CUSTOM") // MATHEMATICAL, STATISTICAL, BUSINESS, CUSTOM
  isTemplate  Boolean   @default(false) @map("is_template") // Whether this is a template formula
  isActive    Boolean   @default(true) @map("is_active")
  variables   Json? // Expected variables as JSON array
  returnType  String    @default("NUMBER") @map("return_type") // NUMBER, STRING, BOOLEAN, DATE
  complexity  Int       @default(1) // Complexity score 1-100
  usageCount  Int       @default(0) @map("usage_count") // How many times used
  createdBy   String    @map("created_by")
  lastUsedAt  DateTime? @map("last_used_at")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // Relations
  creator      users                   @relation(fields: [createdBy], references: [id])
  reportFields report_formula_fields[]
  testCases    formula_test_cases[]

  @@index([category])
  @@index([isTemplate])
  @@index([isActive])
  @@index([createdBy])
  @@index([usageCount])
  @@map("report_formulas")
}

model report_formula_fields {
  id         String   @id @default(uuid())
  reportType String   @map("report_type") // AMC, WARRANTY, SERVICE, SALES, CUSTOMER
  fieldName  String   @map("field_name") // Field identifier in report
  fieldLabel String   @map("field_label") // Display label
  formulaId  String   @map("formula_id")
  isActive   Boolean  @default(true) @map("is_active")
  sortOrder  Int      @default(0) @map("sort_order")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // Relations
  formula report_formulas @relation(fields: [formulaId], references: [id], onDelete: Cascade)

  @@unique([reportType, fieldName])
  @@index([reportType])
  @@index([formulaId])
  @@index([isActive])
  @@map("report_formula_fields")
}

model formula_test_cases {
  id             String    @id @default(uuid())
  formulaId      String    @map("formula_id")
  name           String // Test case name
  description    String? // Test case description
  inputData      Json      @map("input_data") // Test input as JSON
  expectedResult Json      @map("expected_result") // Expected output
  actualResult   Json?     @map("actual_result") // Last test result
  passed         Boolean? // Whether test passed
  lastRunAt      DateTime? @map("last_run_at")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")

  // Relations
  formula report_formulas @relation(fields: [formulaId], references: [id], onDelete: Cascade)

  @@index([formulaId])
  @@index([passed])
  @@map("formula_test_cases")
}

// Email Distribution Lists
model email_distribution_lists {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  emails      String[] // Array of email addresses
  isActive    Boolean  @default(true) @map("is_active")
  createdBy   String   @map("created_by")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  user               users                  @relation(fields: [createdBy], references: [id])
  reportEmailConfigs report_email_configs[]

  @@index([name])
  @@index([isActive])
  @@index([createdBy])
  @@map("email_distribution_lists")
}

// Report Email Configurations
model report_email_configs {
  id                   String   @id @default(uuid())
  reportType           String   @map("report_type") // AMC, WARRANTY, SERVICE, SALES, CUSTOMER
  name                 String // User-friendly name for this configuration
  description          String?
  emailSubject         String   @map("email_subject")
  emailBody            String   @map("email_body")
  emailTemplateId      String?  @map("email_template_id")
  distributionListId   String?  @map("distribution_list_id")
  individualRecipients String[] @map("individual_recipients") // Array of individual email addresses
  includeAttachment    Boolean  @default(true) @map("include_attachment")
  attachmentFormat     String   @default("PDF") @map("attachment_format") // PDF, EXCEL, CSV
  isActive             Boolean  @default(true) @map("is_active")
  createdBy            String   @map("created_by")
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")

  user             users                     @relation(fields: [createdBy], references: [id])
  emailTemplate    EmailTemplate?            @relation(fields: [emailTemplateId], references: [id])
  distributionList email_distribution_lists? @relation(fields: [distributionListId], references: [id])
  emailDeliveries  report_email_deliveries[]

  @@index([reportType])
  @@index([isActive])
  @@index([createdBy])
  @@index([emailTemplateId])
  @@index([distributionListId])
  @@map("report_email_configs")
}

// Report Email Deliveries (tracking)
model report_email_deliveries {
  id               String    @id @default(uuid())
  configId         String    @map("config_id")
  reportType       String    @map("report_type")
  reportParameters Json      @map("report_parameters") // Parameters used to generate the report
  recipients       String[] // Array of email addresses that received the email
  emailSubject     String    @map("email_subject")
  attachmentPath   String?   @map("attachment_path") // Path to generated attachment file
  attachmentFormat String?   @map("attachment_format")
  status           String    @default("PENDING") // PENDING, SENT, FAILED, PARTIAL
  sentCount        Int       @default(0) @map("sent_count")
  failedCount      Int       @default(0) @map("failed_count")
  errorMessages    String[]  @map("error_messages") // Array of error messages for failed deliveries
  sentAt           DateTime? @map("sent_at")
  createdBy        String    @map("created_by")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  config report_email_configs @relation(fields: [configId], references: [id], onDelete: Cascade)
  user   users                @relation(fields: [createdBy], references: [id])

  @@index([configId])
  @@index([reportType])
  @@index([status])
  @@index([sentAt])
  @@index([createdBy])
  @@map("report_email_deliveries")
}
