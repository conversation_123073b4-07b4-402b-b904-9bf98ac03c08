import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { requireAuth } from '@/lib/auth/middleware';
import { getAMCMachineRepository } from '@/lib/repositories';

// AMC Machine creation schema
const createAMCMachineSchema = z.object({
  serialNumber: z.string().min(1, 'Serial number is required'),
  location: z.string().min(1, 'Location is required'),
  section: z.string().optional(),
  productId: z.string().uuid('Valid product ID is required'),
  modelId: z.string().uuid('Valid model ID is required'),
  brandId: z.string().uuid('Valid brand ID is required'),
  amcContractId: z.string().uuid('Valid AMC contract ID is required'),
  installationDate: z.coerce.date().optional(),
  warrantyEndDate: z.coerce.date().optional(),
  isActive: z.boolean().default(true),
  notes: z.string().optional(),
});

/**
 * Get all AMC machines
 * Requires authentication
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 });
    }

    // Get the repository instance
    const machineRepository = getAMCMachineRepository();
    
    // Get all AMC machines with related data
    const amcMachines = await machineRepository.findAll({
      includeContract: true,
      includeProduct: true,
      includeModel: true,
      includeBrand: true,
    });

    return NextResponse.json({
      data: amcMachines,
      meta: {
        total: amcMachines.length,
      },
    });
  } catch (error) {
    console.error('Error fetching AMC machines:', error);
    return NextResponse.json(
      { error: 'Failed to fetch AMC machines' },
      { status: 500 }
    );
  }
}

/**
 * Create a new AMC machine
 * Requires authentication and appropriate role
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 });
    }

    // Check role-based access (Admin, Manager, or Executive can create)
    const userRole = authResult.user?.role?.toUpperCase();
    if (!['ADMIN', 'MANAGER', 'EXECUTIVE'].includes(userRole || '')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create AMC machines' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = createAMCMachineSchema.parse(body);

    // Get the repository instance
    const machineRepository = getAMCMachineRepository();
    
    // Create the new AMC machine
    const newAMCMachine = await machineRepository.create(validatedData);

    return NextResponse.json(newAMCMachine, { status: 201 });
  } catch (error) {
    console.error('Error creating AMC machine:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create AMC machine' },
      { status: 500 }
    );
  }
}
