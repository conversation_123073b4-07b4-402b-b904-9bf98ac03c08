import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth/middleware';

// Search parameters schema
const searchParamsSchema = z.object({
  query: z.string().optional().default(''),
  type: z.enum(['amc', 'warranty', 'all']).optional().default('all'),
  productId: z.string().optional(),
  modelId: z.string().optional(),
  brandId: z.string().optional(),
  serialNumber: z.string().optional(),
  limit: z.coerce.number().int().min(1).max(100).optional().default(10),
  offset: z.coerce.number().int().min(0).optional().default(0),
  includeContract: z.enum(['true', 'false']).optional().default('true'),
  includeProduct: z.enum(['true', 'false']).optional().default('true'),
  includeModel: z.enum(['true', 'false']).optional().default('true'),
  includeBrand: z.enum(['true', 'false']).optional().default('true'),
  includeComponents: z.enum(['true', 'false']).optional().default('false'),
  includeCustomer: z.enum(['true', 'false']).optional().default('false'),
});

/**
 * Search for machines (both AMC and warranty)
 * Requires authentication
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 });
    }

    // Parse and validate search parameters
    const { searchParams } = new URL(request.url);
    const params = Object.fromEntries(searchParams.entries());
    const validatedParams = searchParamsSchema.parse(params);

    const {
      query,
      type,
      productId,
      modelId,
      brandId,
      serialNumber,
      limit,
      offset,
      includeContract,
      includeProduct,
      includeModel,
      includeBrand,
      includeComponents,
      includeCustomer,
    } = validatedParams;

    // Build where clauses
    const amcWhere: any = {};
    const warrantyWhere: any = {};

    // Add query filter
    if (query) {
      const queryFilter = {
        OR: [
          { location: { contains: query, mode: 'insensitive' as const } },
          { serialNumber: { contains: query, mode: 'insensitive' as const } },
          { section: { contains: query, mode: 'insensitive' as const } },
        ],
      };

      amcWhere.OR = queryFilter.OR;
      warrantyWhere.OR = queryFilter.OR;
    }

    // Add specific filters
    if (productId) {
      amcWhere.productId = productId;
      warrantyWhere.productId = productId;
    }

    if (modelId) {
      amcWhere.modelId = modelId;
      warrantyWhere.modelId = modelId;
    }

    if (brandId) {
      amcWhere.brandId = brandId;
      warrantyWhere.brandId = brandId;
    }

    if (serialNumber) {
      amcWhere.serialNumber = { contains: serialNumber, mode: 'insensitive' as const };
      warrantyWhere.serialNumber = { contains: serialNumber, mode: 'insensitive' as const };
    }

    // Build include clauses
    const amcInclude: any = {};
    const warrantyInclude: any = {};

    if (includeContract === 'true') {
      amcInclude.amcContract = includeCustomer === 'true' ? { include: { customer: true } } : true;
      warrantyInclude.warranty = includeCustomer === 'true' ? { include: { customer: true } } : true;
    }

    if (includeProduct === 'true') {
      amcInclude.product = true;
      warrantyInclude.product = true;
    }

    if (includeModel === 'true') {
      amcInclude.model = true;
      warrantyInclude.model = true;
    }

    if (includeBrand === 'true') {
      amcInclude.brand = true;
      warrantyInclude.brand = true;
    }

    if (includeComponents === 'true') {
      amcInclude.components = true;
      warrantyInclude.components = true;
    }

    // Get machines based on type
    let amcMachines: any[] = [];
    let warrantyMachines: any[] = [];
    let totalAmcCount = 0;
    let totalWarrantyCount = 0;

    if (type === 'all' || type === 'amc') {
      amcMachines = await prisma.amc_machines.findMany({
        where: amcWhere,
        take: type === 'all' ? Math.floor(limit / 2) : limit,
        skip: offset,
        orderBy: { createdAt: 'desc' },
        include: amcInclude,
      });

      totalAmcCount = await prisma.amc_machines.count({ where: amcWhere });
    }

    if (type === 'all' || type === 'warranty') {
      warrantyMachines = await prisma.warranty_machines.findMany({
        where: warrantyWhere,
        take: type === 'all' ? Math.ceil(limit / 2) : limit,
        skip: offset,
        orderBy: { createdAt: 'desc' },
        include: warrantyInclude,
      });

      totalWarrantyCount = await prisma.warranty_machines.count({ where: warrantyWhere });
    }

    // Combine and format results
    const machines = [
      ...amcMachines.map((machine) => ({
        ...machine,
        machineType: 'amc',
      })),
      ...warrantyMachines.map((machine) => ({
        ...machine,
        machineType: 'warranty',
      })),
    ];

    // Sort by created date
    machines.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    // Limit to requested number
    const limitedMachines = machines.slice(0, limit);

    return NextResponse.json({
      data: limitedMachines,
      meta: {
        total: totalAmcCount + totalWarrantyCount,
        amcTotal: totalAmcCount,
        warrantyTotal: totalWarrantyCount,
        limit,
        offset,
      },
    });
  } catch (error) {
    console.error('Error searching machines:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to search machines' },
      { status: 500 }
    );
  }
}
