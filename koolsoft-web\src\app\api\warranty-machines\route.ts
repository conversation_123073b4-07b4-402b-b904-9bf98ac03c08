import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { requireAuth } from '@/lib/auth/middleware';
import { getWarrantyMachineRepository } from '@/lib/repositories';

// Warranty Machine creation schema
const createWarrantyMachineSchema = z.object({
  serialNumber: z.string().min(1, 'Serial number is required'),
  location: z.string().min(1, 'Location is required'),
  section: z.string().optional(),
  productId: z.string().uuid('Valid product ID is required'),
  modelId: z.string().uuid('Valid model ID is required'),
  brandId: z.string().uuid('Valid brand ID is required'),
  warrantyId: z.string().uuid('Valid warranty ID is required'),
  installationDate: z.coerce.date().optional(),
  warrantyEndDate: z.coerce.date().optional(),
  isActive: z.boolean().default(true),
  notes: z.string().optional(),
});

/**
 * Get all warranty machines
 * Requires authentication
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 });
    }

    // Get the repository instance
    const machineRepository = getWarrantyMachineRepository();
    
    // Get all warranty machines with related data
    const warrantyMachines = await machineRepository.findAll({
      includeWarranty: true,
      includeProduct: true,
      includeModel: true,
      includeBrand: true,
    });

    return NextResponse.json({
      data: warrantyMachines,
      meta: {
        total: warrantyMachines.length,
      },
    });
  } catch (error) {
    console.error('Error fetching warranty machines:', error);
    return NextResponse.json(
      { error: 'Failed to fetch warranty machines' },
      { status: 500 }
    );
  }
}

/**
 * Create a new warranty machine
 * Requires authentication and appropriate role
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await requireAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 });
    }

    // Check role-based access (Admin, Manager, or Executive can create)
    const userRole = authResult.user?.role?.toUpperCase();
    if (!['ADMIN', 'MANAGER', 'EXECUTIVE'].includes(userRole || '')) {
      return NextResponse.json(
        { error: 'Insufficient permissions to create warranty machines' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = createWarrantyMachineSchema.parse(body);

    // Get the repository instance
    const machineRepository = getWarrantyMachineRepository();
    
    // Create the new warranty machine
    const newWarrantyMachine = await machineRepository.create(validatedData);

    return NextResponse.json(newWarrantyMachine, { status: 201 });
  } catch (error) {
    console.error('Error creating warranty machine:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create warranty machine' },
      { status: 500 }
    );
  }
}
