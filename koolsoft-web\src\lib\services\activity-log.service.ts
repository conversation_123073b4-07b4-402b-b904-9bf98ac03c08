import { getActivityLogRepository } from '../repositories/repository.factory';
import { ActivityLogRepository } from '../repositories/activity-log.repository';
import { NextRequest } from 'next/server';

// Use dynamic imports for server-only modules
let getServerSession: any;
let authOptions: any;

// Only import server-side modules when running on the server
if (typeof window === 'undefined') {
  import('next-auth').then(module => {
    getServerSession = module.getServerSession;
  });

  import('@/lib/auth').then(module => {
    authOptions = module.authOptions;
  });
}

/**
 * Activity Log Service
 *
 * This service provides methods for logging user activities.
 * It abstracts the details of creating and querying activity logs.
 */
export class ActivityLogService {
  private repository: ActivityLogRepository;

  constructor() {
    this.repository = getActivityLogRepository();
  }

  /**
   * Log a user action
   * @param action Action performed
   * @param userId User ID (optional)
   * @param entityType Entity type (optional)
   * @param entityId Entity ID (optional)
   * @param details Additional details (optional)
   * @param ipAddress IP address (optional)
   * @param userAgent User agent (optional)
   * @returns Promise resolving to the created activity log
   */
  async logAction(
    action: string,
    userId?: string,
    entityType?: string,
    entityId?: string,
    details?: any,
    ipAddress?: string,
    userAgent?: string
  ) {
    // This method should only be called on the server
    if (typeof window !== 'undefined') {
      console.error('logAction should only be called on the server');
      return null;
    }

    return this.repository.create({
      action,
      user: userId ? { connect: { id: userId } } : undefined,
      entityType,
      entityId,
      details,
      ipAddress,
      userAgent,
    });
  }

  /**
   * Log an API request
   * @param req Next.js request object
   * @param action Action name
   * @param entityType Entity type (optional)
   * @param entityId Entity ID (optional)
   * @param details Additional details (optional)
   * @returns Promise resolving to the created activity log
   */
  async logApiRequest(
    req: NextRequest,
    action: string,
    entityType?: string,
    entityId?: string,
    details?: any
  ) {
    // This method should only be called on the server
    if (typeof window !== 'undefined') {
      console.error('logApiRequest should only be called on the server');
      return null;
    }

    // Dynamically import server-only modules if needed
    if (!getServerSession || !authOptions) {
      const [nextAuthModule, authModule] = await Promise.all([
        import('next-auth'),
        import('@/lib/auth')
      ]);

      getServerSession = nextAuthModule.getServerSession;
      authOptions = authModule.authOptions;
    }

    // Get user from session
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;

    // Get IP address
    const ipAddress = req.headers.get('x-forwarded-for') ||
                      req.headers.get('x-real-ip') ||
                      'unknown';

    // Get user agent
    const userAgent = req.headers.get('user-agent') || 'unknown';

    // Create log entry
    return this.logAction(
      action,
      userId,
      entityType,
      entityId,
      details,
      ipAddress as string,
      userAgent
    );
  }

  /**
   * Log an authentication event
   * @param action Authentication action (e.g., 'login', 'logout', 'failed_login')
   * @param userId User ID (optional)
   * @param details Additional details (optional)
   * @param ipAddress IP address (optional)
   * @param userAgent User agent (optional)
   * @returns Promise resolving to the created activity log
   */
  async logAuthEvent(
    action: string,
    userId?: string,
    details?: any,
    ipAddress?: string,
    userAgent?: string
  ) {
    // This method should only be called on the server
    if (typeof window !== 'undefined') {
      console.error('logAuthEvent should only be called on the server');
      return null;
    }

    return this.logAction(
      action,
      userId,
      'auth',
      userId,
      details,
      ipAddress,
      userAgent
    );
  }

  /**
   * Log a system event
   * @param action System action
   * @param details Additional details (optional)
   * @returns Promise resolving to the created activity log
   */
  async logSystemEvent(action: string, details?: any) {
    // This method should only be called on the server
    if (typeof window !== 'undefined') {
      console.error('logSystemEvent should only be called on the server');
      return null;
    }

    return this.logAction(
      action,
      undefined,
      'system',
      undefined,
      details
    );
  }

  /**
   * Find activity logs with filters
   * @param filter Filter options
   * @param skip Number of records to skip
   * @param take Maximum number of records to return
   * @returns Promise resolving to activity logs and total count
   */
  async findLogs(
    filter: {
      userId?: string;
      action?: string;
      entityType?: string;
      entityId?: string;
      startDate?: Date;
      endDate?: Date;
      search?: string;
    },
    skip?: number,
    take?: number
  ) {
    // This method should only be called on the server
    if (typeof window !== 'undefined') {
      console.error('findLogs should only be called on the server');
      return { logs: [], total: 0 };
    }

    return this.repository.findWithFilters(filter, skip, take);
  }

  /**
   * Get common actions for filtering
   * @returns Array of common actions
   */
  getCommonActions() {
    return [
      { value: 'login', label: 'Login' },
      { value: 'logout', label: 'Logout' },
      { value: 'failed_login', label: 'Failed Login' },
      { value: 'create_user', label: 'Create User' },
      { value: 'update_user', label: 'Update User' },
      { value: 'delete_user', label: 'Delete User' },
      { value: 'create_customer', label: 'Create Customer' },
      { value: 'update_customer', label: 'Update Customer' },
      { value: 'delete_customer', label: 'Delete Customer' },
      { value: 'create_amc_contract', label: 'Create AMC Contract' },
      { value: 'update_amc_contract', label: 'Update AMC Contract' },
      { value: 'delete_amc_contract', label: 'Delete AMC Contract' },
      { value: 'send_email', label: 'Send Email' },
      { value: 'export_data', label: 'Export Data' },
      { value: 'import_data', label: 'Import Data' },
    ];
  }

  /**
   * Get common entity types for filtering
   * @returns Array of common entity types
   */
  getCommonEntityTypes() {
    return [
      { value: 'auth', label: 'Authentication' },
      { value: 'user', label: 'User' },
      { value: 'customer', label: 'Customer' },
      { value: 'amc_contract', label: 'AMC Contract' },
      { value: 'amc_machine', label: 'AMC Machine' },
      { value: 'history_detail', label: 'History Detail' },
      { value: 'visit_card', label: 'Visit Card' },
      { value: 'email', label: 'Email' },
      { value: 'system', label: 'System' },
    ];
  }

  /**
   * Check if we're running on the server
   * @returns True if running on the server, false if running in the browser
   */
  isServer() {
    return typeof window === 'undefined';
  }
}

// Singleton instances - separate for client and server
let serverActivityLogServiceInstance: ActivityLogService | null = null;
let clientActivityLogServiceInstance: any = null;

/**
 * Get the activity log service instance
 * @returns Activity log service instance
 */
export function getActivityLogService(): ActivityLogService {
  // For client-side, return a limited version with only client-safe methods
  if (typeof window !== 'undefined') {
    if (!clientActivityLogServiceInstance) {
      // Create a client-side safe version with only the methods that don't access the server
      clientActivityLogServiceInstance = {
        getCommonActions: () => [
          { value: 'login', label: 'Login' },
          { value: 'logout', label: 'Logout' },
          { value: 'failed_login', label: 'Failed Login' },
          { value: 'create_user', label: 'Create User' },
          { value: 'update_user', label: 'Update User' },
          { value: 'delete_user', label: 'Delete User' },
          { value: 'create_customer', label: 'Create Customer' },
          { value: 'update_customer', label: 'Update Customer' },
          { value: 'delete_customer', label: 'Delete Customer' },
          { value: 'create_amc_contract', label: 'Create AMC Contract' },
          { value: 'update_amc_contract', label: 'Update AMC Contract' },
          { value: 'delete_amc_contract', label: 'Delete AMC Contract' },
          { value: 'send_email', label: 'Send Email' },
          { value: 'export_data', label: 'Export Data' },
          { value: 'import_data', label: 'Import Data' },
        ],
        getCommonEntityTypes: () => [
          { value: 'auth', label: 'Authentication' },
          { value: 'user', label: 'User' },
          { value: 'customer', label: 'Customer' },
          { value: 'amc_contract', label: 'AMC Contract' },
          { value: 'amc_machine', label: 'AMC Machine' },
          { value: 'history_detail', label: 'History Detail' },
          { value: 'visit_card', label: 'Visit Card' },
          { value: 'email', label: 'Email' },
          { value: 'system', label: 'System' },
        ],
        isServer: () => false
      };
    }
    return clientActivityLogServiceInstance as ActivityLogService;
  }

  // For server-side, return the full service
  if (!serverActivityLogServiceInstance) {
    serverActivityLogServiceInstance = new ActivityLogService();
  }
  return serverActivityLogServiceInstance;
}
